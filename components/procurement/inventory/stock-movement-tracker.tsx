'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  Package, 
  Search, 
  Filter,
  Download,
  RefreshCw,
  ArrowUpCircle,
  ArrowDownCircle,
  AlertTriangle
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface StockMovement {
  _id: string;
  inventoryId: string;
  itemName: string;
  movementType: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  unit: string;
  reason: string;
  reference?: string;
  previousStock: number;
  newStock: number;
  unitPrice?: number;
  totalValue?: number;
  location: string;
  performedBy: string;
  createdAt: Date;
  notes?: string;
}

interface StockMovementTrackerProps {
  inventoryId?: string;
  showFilters?: boolean;
  maxHeight?: string;
}

export function StockMovementTracker({ 
  inventoryId, 
  showFilters = true,
  maxHeight = "400px"
}: StockMovementTrackerProps) {
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [movementTypeFilter, setMovementTypeFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('7days');

  useEffect(() => {
    fetchMovements();
  }, [inventoryId, movementTypeFilter, dateRange]);

  const fetchMovements = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (inventoryId) params.append('inventoryId', inventoryId);
      if (movementTypeFilter !== 'all') params.append('movementType', movementTypeFilter);
      params.append('dateRange', dateRange);
      params.append('limit', '50');

      const response = await fetch(`/api/procurement/inventory/movements?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch stock movements');
      }

      const data = await response.json();
      setMovements(data.data || []);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch stock movements';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredMovements = movements.filter(movement => {
    const matchesSearch = !searchTerm || 
      movement.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.reference?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'in':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />;
      case 'out':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />;
      case 'adjustment':
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'transfer':
        return <Package className="h-4 w-4 text-blue-600" />;
      default:
        return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getMovementBadge = (type: string) => {
    const config = {
      in: { variant: 'default' as const, label: 'Stock In' },
      out: { variant: 'destructive' as const, label: 'Stock Out' },
      adjustment: { variant: 'secondary' as const, label: 'Adjustment' },
      transfer: { variant: 'outline' as const, label: 'Transfer' }
    };
    
    const { variant, label } = config[type as keyof typeof config] || { variant: 'outline' as const, label: type };
    return <Badge variant={variant}>{label}</Badge>;
  };

  const exportMovements = () => {
    // Create CSV content
    const headers = ['Date', 'Item', 'Type', 'Quantity', 'Previous Stock', 'New Stock', 'Reason', 'Reference', 'Performed By'];
    const csvContent = [
      headers.join(','),
      ...filteredMovements.map(movement => [
        new Date(movement.createdAt).toLocaleDateString(),
        `"${movement.itemName}"`,
        movement.movementType,
        movement.quantity,
        movement.previousStock,
        movement.newStock,
        `"${movement.reason}"`,
        movement.reference || '',
        `"${movement.performedBy}"`
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `stock-movements-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Stock movements exported successfully',
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Stock Movement History
            </CardTitle>
            <CardDescription>
              Track all inventory stock movements and changes
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={fetchMovements} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={exportMovements}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search movements..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={movementTypeFilter} onValueChange={setMovementTypeFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Movement Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="in">Stock In</SelectItem>
                <SelectItem value="out">Stock Out</SelectItem>
                <SelectItem value="adjustment">Adjustment</SelectItem>
                <SelectItem value="transfer">Transfer</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 days</SelectItem>
                <SelectItem value="30days">Last 30 days</SelectItem>
                <SelectItem value="90days">Last 90 days</SelectItem>
                <SelectItem value="1year">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Movements Table */}
        <div className="rounded-md border" style={{ maxHeight, overflowY: 'auto' }}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Item</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Stock Change</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Performed By</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    Loading movements...
                  </TableCell>
                </TableRow>
              ) : filteredMovements.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    No stock movements found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredMovements.map((movement) => (
                  <TableRow key={movement._id}>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(movement.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(movement.createdAt).toLocaleTimeString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{movement.itemName}</div>
                      <div className="text-xs text-muted-foreground">{movement.location}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getMovementIcon(movement.movementType)}
                        {getMovementBadge(movement.movementType)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {movement.movementType === 'out' ? '-' : '+'}{movement.quantity} {movement.unit}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {movement.previousStock} → {movement.newStock}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {movement.movementType === 'in' ? (
                          <span className="text-green-600">+{movement.quantity}</span>
                        ) : (
                          <span className="text-red-600">-{movement.quantity}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{movement.reason}</div>
                      {movement.reference && (
                        <div className="text-xs text-muted-foreground">Ref: {movement.reference}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{movement.performedBy}</div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Summary */}
        {filteredMovements.length > 0 && (
          <div className="mt-4 text-sm text-muted-foreground">
            Showing {filteredMovements.length} movement{filteredMovements.length !== 1 ? 's' : ''}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
