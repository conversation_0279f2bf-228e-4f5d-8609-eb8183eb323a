'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  AlertTriangle, 
  Package, 
  ShoppingCart,
  TrendingDown,
  Clock,
  DollarSign,
  RefreshCw,
  CheckCircle
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface ReorderSuggestion {
  _id: string;
  inventoryId: string;
  itemName: string;
  sku?: string;
  currentStock: number;
  minimumStock: number;
  reorderPoint: number;
  suggestedQuantity: number;
  unit: string;
  unitPrice: number;
  estimatedCost: number;
  currency: string;
  leadTime: number;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  preferredSupplier?: {
    _id: string;
    name: string;
    contactInfo?: string;
  };
  lastOrderDate?: Date;
  averageConsumption: number;
  stockoutRisk: number;
  category: string;
  location: string;
}

interface ReorderSuggestionsProps {
  onCreatePurchaseOrder?: (items: ReorderSuggestion[]) => void;
  maxHeight?: string;
}

export function ReorderSuggestions({ 
  onCreatePurchaseOrder,
  maxHeight = "500px"
}: ReorderSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<ReorderSuggestion[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchReorderSuggestions();
  }, []);

  const fetchReorderSuggestions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/procurement/inventory/reorder');
      
      if (!response.ok) {
        throw new Error('Failed to fetch reorder suggestions');
      }

      const data = await response.json();
      setSuggestions(data.data || []);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch reorder suggestions';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectItem = (itemId: string, selected: boolean) => {
    setSelectedItems(prev =>
      selected
        ? [...prev, itemId]
        : prev.filter(id => id !== itemId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedItems(selected ? suggestions.map(item => item._id) : []);
  };

  const handleCreatePurchaseOrder = () => {
    const selectedSuggestions = suggestions.filter(item => selectedItems.includes(item._id));
    
    if (selectedSuggestions.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select items to create purchase order',
        variant: 'destructive',
      });
      return;
    }

    if (onCreatePurchaseOrder) {
      onCreatePurchaseOrder(selectedSuggestions);
    } else {
      // Default behavior - show success message
      toast({
        title: 'Purchase Order Created',
        description: `Created purchase order for ${selectedSuggestions.length} items`,
      });
    }

    setSelectedItems([]);
  };

  const getPriorityBadge = (priority: string) => {
    const config = {
      high: { variant: 'destructive' as const, label: 'High Priority' },
      medium: { variant: 'secondary' as const, label: 'Medium Priority' },
      low: { variant: 'outline' as const, label: 'Low Priority' }
    };
    
    const { variant, label } = config[priority as keyof typeof config] || { variant: 'outline' as const, label: priority };
    return <Badge variant={variant}>{label}</Badge>;
  };

  const getRiskBadge = (risk: number) => {
    if (risk >= 80) {
      return <Badge variant="destructive">Critical Risk</Badge>;
    } else if (risk >= 60) {
      return <Badge variant="secondary">High Risk</Badge>;
    } else if (risk >= 40) {
      return <Badge variant="outline">Medium Risk</Badge>;
    } else {
      return <Badge variant="default">Low Risk</Badge>;
    }
  };

  const totalEstimatedCost = suggestions
    .filter(item => selectedItems.includes(item._id))
    .reduce((sum, item) => sum + item.estimatedCost, 0);

  const highPriorityCount = suggestions.filter(item => item.priority === 'high').length;
  const criticalRiskCount = suggestions.filter(item => item.stockoutRisk >= 80).length;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Reorder Suggestions
            </CardTitle>
            <CardDescription>
              Items that need restocking based on current stock levels and consumption patterns
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={fetchReorderSuggestions} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {selectedItems.length > 0 && (
              <Button size="sm" onClick={handleCreatePurchaseOrder}>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Create PO ({selectedItems.length})
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        {suggestions.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold">{suggestions.length}</div>
              <div className="text-xs text-muted-foreground">Items Need Reorder</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold text-red-600">{highPriorityCount}</div>
              <div className="text-xs text-muted-foreground">High Priority</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-2xl font-bold text-orange-600">{criticalRiskCount}</div>
              <div className="text-xs text-muted-foreground">Critical Risk</div>
            </div>
            <div className="bg-muted/50 p-3 rounded-md text-center">
              <div className="text-lg font-bold">MWK {totalEstimatedCost.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Selected Cost</div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Bulk Actions Bar */}
        {selectedItems.length > 0 && (
          <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-md mb-4">
            <div className="text-sm">
              {selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'} selected
              {totalEstimatedCost > 0 && (
                <span className="ml-2 font-medium">
                  (Total: MWK {totalEstimatedCost.toLocaleString()})
                </span>
              )}
            </div>
            <Button size="sm" onClick={handleCreatePurchaseOrder}>
              <ShoppingCart className="h-4 w-4 mr-2" />
              Create Purchase Order
            </Button>
          </div>
        )}

        {/* Suggestions Table */}
        <div className="rounded-md border" style={{ maxHeight, overflowY: 'auto' }}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={suggestions.length > 0 && selectedItems.length === suggestions.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </TableHead>
                <TableHead>Item</TableHead>
                <TableHead>Current Stock</TableHead>
                <TableHead>Suggested Qty</TableHead>
                <TableHead>Estimated Cost</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Risk</TableHead>
                <TableHead>Lead Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    Loading reorder suggestions...
                  </TableCell>
                </TableRow>
              ) : suggestions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    <div className="flex flex-col items-center gap-2">
                      <CheckCircle className="h-12 w-12 text-green-500 opacity-50" />
                      <p>No reorder suggestions</p>
                      <p className="text-sm text-muted-foreground">All items are adequately stocked</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                suggestions.map((item) => (
                  <TableRow key={item._id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedItems.includes(item._id)}
                        onCheckedChange={(checked) => handleSelectItem(item._id, !!checked)}
                        aria-label={`Select ${item.itemName}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.itemName}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.sku && `SKU: ${item.sku} • `}
                          {item.category}
                        </div>
                        {item.preferredSupplier && (
                          <div className="text-xs text-muted-foreground">
                            Supplier: {item.preferredSupplier.name}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium text-red-600">
                          {item.currentStock} {item.unit}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Min: {item.minimumStock} • Reorder: {item.reorderPoint}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {item.suggestedQuantity} {item.unit}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Avg consumption: {item.averageConsumption}/{item.unit}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {item.currency} {item.estimatedCost.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        @ {item.currency} {item.unitPrice.toLocaleString()}/{item.unit}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getPriorityBadge(item.priority)}
                    </TableCell>
                    <TableCell>
                      {getRiskBadge(item.stockoutRisk)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span className="text-sm">{item.leadTime} days</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Summary */}
        {suggestions.length > 0 && (
          <div className="mt-4 text-sm text-muted-foreground">
            Showing {suggestions.length} reorder suggestion{suggestions.length !== 1 ? 's' : ''}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
