// components/procurement/procurement-dashboard.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  ShoppingCart, 
  Package, 
  Truck, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  FileText,
  BarChart3,
  PlusCircle,
  Eye,
  Edit,
  MoreHorizontal,
  Loader2
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { useProcurementStore } from "@/lib/stores/procurement-store"

// Mock data for demonstration
const mockStats = {
  totalPurchaseOrders: 156,
  pendingRequisitions: 23,
  activeSuppliers: 45,
  monthlySpend: 2450000,
  avgProcessingTime: 5.2,
  complianceScore: 94
}

// Remove static data - will be replaced with store data

const getStatusBadge = (status: string) => {
  const statusConfig = {
    approved: { variant: "default" as const, label: "Approved" },
    pending: { variant: "secondary" as const, label: "Pending" },
    delivered: { variant: "default" as const, label: "Delivered" },
    in_transit: { variant: "secondary" as const, label: "In Transit" },
    pending_approval: { variant: "destructive" as const, label: "Pending Approval" },
    draft: { variant: "outline" as const, label: "Draft" }
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || { variant: "outline" as const, label: status }
  return <Badge variant={config.variant}>{config.label}</Badge>
}

const getPriorityBadge = (priority: string) => {
  const priorityConfig = {
    high: { variant: "destructive" as const, label: "High" },
    medium: { variant: "secondary" as const, label: "Medium" },
    low: { variant: "outline" as const, label: "Low" }
  }
  
  const config = priorityConfig[priority as keyof typeof priorityConfig] || { variant: "outline" as const, label: priority }
  return <Badge variant={config.variant}>{config.label}</Badge>
}

export function ProcurementDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // Get data from procurement store
  const {
    contracts,
    deliveries,
    inventory,
    stockLevelReport,
    isLoadingContracts,
    isLoadingDeliveries,
    isLoadingInventory,
    fetchContracts,
    fetchDeliveries,
    fetchInventory,
    fetchStockLevelReport
  } = useProcurementStore()

  // Fetch data on component mount
  useEffect(() => {
    fetchContracts(1, 10) // Get recent contracts
    fetchDeliveries(1, 10) // Get recent deliveries
    fetchInventory(1, 10) // Get inventory overview
    fetchStockLevelReport() // Get stock level report
  }, [fetchContracts, fetchDeliveries, fetchInventory, fetchStockLevelReport])

  // Mock data for orders and requisitions until store is fully implemented
  const mockRecentOrders = contracts.slice(0, 4).map(contract => ({
    id: contract.contractNumber,
    supplier: contract.supplier.name,
    amount: contract.value,
    status: contract.status,
    date: new Date(contract.createdAt).toLocaleDateString(),
    items: contract.title
  }))

  const mockRequisitions = [
    {
      id: "REQ-2024-015",
      department: "IT Department",
      requestor: "John Banda",
      amount: 450000,
      status: "pending_approval",
      priority: "high",
      date: "2024-01-15"
    },
    {
      id: "REQ-2024-016",
      department: "HR Department",
      requestor: "Mary Phiri",
      amount: 125000,
      status: "approved",
      priority: "medium",
      date: "2024-01-14"
    },
    {
      id: "REQ-2024-017",
      department: "Finance",
      requestor: "Peter Mwale",
      amount: 85000,
      status: "draft",
      priority: "low",
      date: "2024-01-13"
    }
  ]

  return (
    <DashboardShell>
      <DashboardHeader 
        heading="Procurement Management"
        text="Manage purchase orders, suppliers, and procurement processes"
      >
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/dashboard/procurement/requisitions">
              <PlusCircle className="mr-2 h-4 w-4" />
              New Requisition
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard/procurement/reports">
              <BarChart3 className="mr-2 h-4 w-4" />
              Reports
            </Link>
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Purchase Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.totalPurchaseOrders}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +12% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Requisitions</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.pendingRequisitions}</div>
              <p className="text-xs text-muted-foreground">
                <AlertTriangle className="inline h-3 w-3 mr-1" />
                Requires attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inventory Items</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stockLevelReport?.totalItems || inventory?.length || 0}</div>
              <p className="text-xs text-muted-foreground">
                <Package className="inline h-3 w-3 mr-1" />
                Total tracked items
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stockLevelReport?.lowStock || 0}</div>
              <p className="text-xs text-muted-foreground">
                <AlertTriangle className="inline h-3 w-3 mr-1" />
                Need restocking
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.activeSuppliers}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +3 new this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Spend</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">MWK {mockStats.monthlySpend.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingDown className="inline h-3 w-3 mr-1" />
                -5% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="orders">Recent Orders</TabsTrigger>
            <TabsTrigger value="requisitions">Requisitions</TabsTrigger>
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Procurement Overview</CardTitle>
                </CardHeader>
                <CardContent className="pl-2">
                  <div className="h-[350px] flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Procurement analytics chart will be displayed here</p>
                      <p className="text-sm">Monthly spending trends and performance metrics</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest procurement activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Purchase Order PO-2024-001 approved</p>
                        <p className="text-xs text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">New supplier Tech Solutions Inc added</p>
                        <p className="text-xs text-muted-foreground">4 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Requisition REQ-2024-015 pending approval</p>
                        <p className="text-xs text-muted-foreground">6 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Delivery DEL-2024-003 completed</p>
                        <p className="text-xs text-muted-foreground">1 day ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="orders" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Purchase Orders</CardTitle>
                <CardDescription>Latest purchase orders and their status</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingContracts ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading orders...</span>
                  </div>
                ) : mockRecentOrders.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent orders found</p>
                    <p className="text-sm">Create your first purchase order to get started</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {mockRecentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{order.id}</span>
                          {getStatusBadge(order.status)}
                        </div>
                        <p className="text-sm text-muted-foreground">{order.supplier}</p>
                        <p className="text-xs text-muted-foreground">{order.items}</p>
                      </div>
                      <div className="text-right space-y-1">
                        <p className="font-medium">MWK {order.amount.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">{order.date}</p>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Order
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="requisitions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Purchase Requisitions</CardTitle>
                <CardDescription>Pending and recent purchase requisitions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRequisitions.map((req) => (
                    <div key={req.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{req.id}</span>
                          {getStatusBadge(req.status)}
                          {getPriorityBadge(req.priority)}
                        </div>
                        <p className="text-sm text-muted-foreground">{req.department}</p>
                        <p className="text-xs text-muted-foreground">Requested by: {req.requestor}</p>
                      </div>
                      <div className="text-right space-y-1">
                        <p className="font-medium">MWK {req.amount.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">{req.date}</p>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Approve
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inventory" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Inventory Overview</CardTitle>
                  <CardDescription>Current stock levels and alerts</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingInventory ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span className="ml-2">Loading inventory...</span>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-muted/50 rounded-md">
                          <div className="text-2xl font-bold">{stockLevelReport?.totalItems || inventory?.length || 0}</div>
                          <div className="text-xs text-muted-foreground">Total Items</div>
                        </div>
                        <div className="text-center p-4 bg-muted/50 rounded-md">
                          <div className="text-2xl font-bold text-orange-600">{stockLevelReport?.lowStock || 0}</div>
                          <div className="text-xs text-muted-foreground">Low Stock</div>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-muted/50 rounded-md">
                          <div className="text-2xl font-bold text-red-600">{stockLevelReport?.outOfStock || 0}</div>
                          <div className="text-xs text-muted-foreground">Out of Stock</div>
                        </div>
                        <div className="text-center p-4 bg-muted/50 rounded-md">
                          <div className="text-2xl font-bold">MWK {(stockLevelReport?.totalValue || 0).toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">Total Value</div>
                        </div>
                      </div>
                      {(stockLevelReport?.lowStock || 0) > 0 && (
                        <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-orange-600" />
                            <span className="text-sm font-medium text-orange-800">
                              {stockLevelReport?.lowStock} items need restocking
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Inventory Activity</CardTitle>
                  <CardDescription>Latest stock movements and updates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {inventory && inventory.length > 0 ? (
                      inventory.slice(0, 5).map((item) => (
                        <div key={item._id} className="flex items-center justify-between p-3 border rounded-md">
                          <div className="space-y-1">
                            <div className="font-medium text-sm">{item.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Stock: {item.currentStock} {item.unit}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              MWK {(item.totalValue || 0).toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {item.currentStock <= item.minimumStock ? (
                                <span className="text-orange-600">Low Stock</span>
                              ) : item.currentStock === 0 ? (
                                <span className="text-red-600">Out of Stock</span>
                              ) : (
                                <span className="text-green-600">In Stock</span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No inventory items found</p>
                        <p className="text-sm">Add inventory items to get started</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Average Processing Time</span>
                    <span className="font-medium">{mockStats.avgProcessingTime} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Compliance Score</span>
                    <span className="font-medium">{mockStats.complianceScore}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>On-time Delivery Rate</span>
                    <span className="font-medium">87%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost Savings</span>
                    <span className="font-medium text-green-600">MWK 125,000</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/procurement/requisitions">
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Create New Requisition
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/procurement/inventory">
                      <Package className="mr-2 h-4 w-4" />
                      Manage Inventory
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/procurement/suppliers">
                      <Users className="mr-2 h-4 w-4" />
                      Manage Suppliers
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/procurement/reports">
                      <FileText className="mr-2 h-4 w-4" />
                      Generate Reports
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  )
}
