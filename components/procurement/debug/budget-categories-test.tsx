"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useProcurementStore } from "@/lib/stores/procurement-store"
import { RefreshCw, Database, AlertCircle, CheckCircle, DollarSign } from "lucide-react"

export function BudgetCategoriesTest() {
  const [testResults, setTestResults] = useState<any>({});
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  const {
    budgetCategories,
    isLoadingBudgetCategories,
    budgetCategoriesError,
    fetchBudgetCategories
  } = useProcurementStore();

  // Test direct API calls
  const testDirectAPI = async () => {
    setIsTestingAPI(true);
    const results: any = {};

    try {
      // Test Budget Categories API - Main endpoint
      console.log('Testing Budget Categories API (main)...');
      const response1 = await fetch('/api/accounting/budget/categories?type=expense&limit=100');
      console.log('Budget Categories Response status:', response1.status);
      
      if (response1.ok) {
        const data1 = await response1.json();
        console.log('Budget Categories Data:', data1);
        results.mainAPI = {
          status: 'success',
          count: data1.categories?.length || 0,
          data: data1
        };
      } else {
        const errorText = await response1.text();
        console.error('Budget Categories Error:', errorText);
        results.mainAPI = {
          status: 'error',
          error: errorText
        };
      }
    } catch (error) {
      console.error('Budget Categories API Error:', error);
      results.mainAPI = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    try {
      // Test Alternative Budget Categories API
      console.log('Testing Alternative Budget Categories API...');
      const response2 = await fetch('/api/accounting/budget/category?limit=100');
      console.log('Alternative Budget Categories Response status:', response2.status);
      
      if (response2.ok) {
        const data2 = await response2.json();
        console.log('Alternative Budget Categories Data:', data2);
        results.altAPI = {
          status: 'success',
          count: data2.categories?.length || 0,
          data: data2
        };
      } else {
        const errorText = await response2.text();
        console.error('Alternative Budget Categories Error:', errorText);
        results.altAPI = {
          status: 'error',
          error: errorText
        };
      }
    } catch (error) {
      console.error('Alternative Budget Categories API Error:', error);
      results.altAPI = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    try {
      // Test Project Budget Categories API
      console.log('Testing Project Budget Categories API...');
      const response3 = await fetch('/api/project/budget/category?limit=100');
      console.log('Project Budget Categories Response status:', response3.status);
      
      if (response3.ok) {
        const data3 = await response3.json();
        console.log('Project Budget Categories Data:', data3);
        results.projectAPI = {
          status: 'success',
          count: data3.docs?.length || data3.categories?.length || 0,
          data: data3
        };
      } else {
        const errorText = await response3.text();
        console.error('Project Budget Categories Error:', errorText);
        results.projectAPI = {
          status: 'error',
          error: errorText
        };
      }
    } catch (error) {
      console.error('Project Budget Categories API Error:', error);
      results.projectAPI = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    setTestResults(results);
    setIsTestingAPI(false);
  };

  // Test store functions
  const testStoreFunctions = async () => {
    console.log('Testing budget categories store function...');
    await fetchBudgetCategories();
  };

  useEffect(() => {
    // Auto-test on component mount
    testDirectAPI();
    testStoreFunctions();
  }, []);

  const getStatusBadge = (status: string) => {
    if (status === 'success') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Success</Badge>;
    } else if (status === 'error') {
      return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>;
    } else {
      return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Budget Categories Test Dashboard
          </CardTitle>
          <CardDescription>
            Test budget categories fetching for Contract forms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={testDirectAPI} disabled={isTestingAPI}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isTestingAPI ? 'animate-spin' : ''}`} />
              Test Direct APIs
            </Button>
            <Button onClick={testStoreFunctions} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Store Function
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Direct API Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Direct API Test Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {testResults.mainAPI && (
            <div>
              <h4 className="font-medium flex items-center gap-2">
                Main Budget Categories API (/api/accounting/budget/categories)
                {getStatusBadge(testResults.mainAPI.status)}
              </h4>
              {testResults.mainAPI.status === 'success' ? (
                <div className="text-sm text-muted-foreground">
                  Found {testResults.mainAPI.count} budget categories
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Error: {testResults.mainAPI.error}
                </div>
              )}
            </div>
          )}

          <Separator />

          {testResults.altAPI && (
            <div>
              <h4 className="font-medium flex items-center gap-2">
                Alternative Budget Categories API (/api/accounting/budget/category)
                {getStatusBadge(testResults.altAPI.status)}
              </h4>
              {testResults.altAPI.status === 'success' ? (
                <div className="text-sm text-muted-foreground">
                  Found {testResults.altAPI.count} budget categories
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Error: {testResults.altAPI.error}
                </div>
              )}
            </div>
          )}

          <Separator />

          {testResults.projectAPI && (
            <div>
              <h4 className="font-medium flex items-center gap-2">
                Project Budget Categories API (/api/project/budget/category)
                {getStatusBadge(testResults.projectAPI.status)}
              </h4>
              {testResults.projectAPI.status === 'success' ? (
                <div className="text-sm text-muted-foreground">
                  Found {testResults.projectAPI.count} budget categories
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Error: {testResults.projectAPI.error}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Store State Results */}
      <Card>
        <CardHeader>
          <CardTitle>Store State Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium flex items-center gap-2">
              Budget Categories Store
              {isLoadingBudgetCategories ? (
                <Badge variant="outline">Loading...</Badge>
              ) : budgetCategoriesError ? (
                <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>
              ) : (
                <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Loaded</Badge>
              )}
            </h4>
            <div className="text-sm text-muted-foreground">
              {isLoadingBudgetCategories ? (
                'Loading budget categories...'
              ) : budgetCategoriesError ? (
                `Error: ${budgetCategoriesError}`
              ) : (
                `${budgetCategories.length} budget categories in store`
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Preview */}
      {budgetCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Budget Categories Preview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium">Available Budget Categories:</h4>
              <div className="text-sm space-y-1">
                {budgetCategories.slice(0, 10).map((category, index) => (
                  <div key={index} className="bg-gray-50 p-2 rounded">
                    <span className="font-medium">{category.name}</span>
                    {category.description && (
                      <span className="text-muted-foreground ml-2">- {category.description}</span>
                    )}
                    <span className="text-xs text-blue-600 ml-2">ID: {category._id}</span>
                  </div>
                ))}
                {budgetCategories.length > 10 && (
                  <div className="text-xs text-muted-foreground">
                    ... and {budgetCategories.length - 10} more categories
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
