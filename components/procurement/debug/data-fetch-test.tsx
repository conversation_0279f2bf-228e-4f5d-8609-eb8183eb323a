"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useProcurementStore } from "@/lib/stores/procurement-store"
import { RefreshCw, Database, AlertCircle, CheckCircle } from "lucide-react"

export function DataFetchTest() {
  const [testResults, setTestResults] = useState<any>({});
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  const {
    purchaseOrders,
    suppliers,
    isLoadingPurchaseOrders,
    isLoadingSuppliers,
    purchaseOrdersError,
    suppliersError,
    fetchPurchaseOrders,
    fetchSuppliers
  } = useProcurementStore();

  // Test direct API calls
  const testDirectAPI = async () => {
    setIsTestingAPI(true);
    const results: any = {};

    try {
      // Test Purchase Orders API
      console.log('Testing Purchase Orders API...');
      const poResponse = await fetch('/api/procurement/purchase-orders?limit=5');
      console.log('PO Response status:', poResponse.status);
      
      if (poResponse.ok) {
        const poData = await poResponse.json();
        console.log('PO Data:', poData);
        results.purchaseOrders = {
          status: 'success',
          count: poData.purchaseOrders?.length || 0,
          data: poData
        };
      } else {
        const errorText = await poResponse.text();
        console.error('PO Error:', errorText);
        results.purchaseOrders = {
          status: 'error',
          error: errorText
        };
      }
    } catch (error) {
      console.error('PO API Error:', error);
      results.purchaseOrders = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    try {
      // Test Suppliers API
      console.log('Testing Suppliers API...');
      const supplierResponse = await fetch('/api/procurement/suppliers?limit=5');
      console.log('Supplier Response status:', supplierResponse.status);
      
      if (supplierResponse.ok) {
        const supplierData = await supplierResponse.json();
        console.log('Supplier Data:', supplierData);
        results.suppliers = {
          status: 'success',
          count: supplierData.data?.length || 0,
          data: supplierData
        };
      } else {
        const errorText = await supplierResponse.text();
        console.error('Supplier Error:', errorText);
        results.suppliers = {
          status: 'error',
          error: errorText
        };
      }
    } catch (error) {
      console.error('Supplier API Error:', error);
      results.suppliers = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    setTestResults(results);
    setIsTestingAPI(false);
  };

  // Test store functions
  const testStoreFunctions = async () => {
    console.log('Testing store functions...');
    await fetchPurchaseOrders();
    await fetchSuppliers();
  };

  useEffect(() => {
    // Auto-test on component mount
    testDirectAPI();
    testStoreFunctions();
  }, []);

  const getStatusBadge = (status: string) => {
    if (status === 'success') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Success</Badge>;
    } else if (status === 'error') {
      return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>;
    } else {
      return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Fetch Test Dashboard
          </CardTitle>
          <CardDescription>
            Test data fetching for Purchase Orders and Suppliers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={testDirectAPI} disabled={isTestingAPI}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isTestingAPI ? 'animate-spin' : ''}`} />
              Test Direct API
            </Button>
            <Button onClick={testStoreFunctions} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Store Functions
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Direct API Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Direct API Test Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {testResults.purchaseOrders && (
            <div>
              <h4 className="font-medium flex items-center gap-2">
                Purchase Orders API
                {getStatusBadge(testResults.purchaseOrders.status)}
              </h4>
              {testResults.purchaseOrders.status === 'success' ? (
                <div className="text-sm text-muted-foreground">
                  Found {testResults.purchaseOrders.count} purchase orders
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Error: {testResults.purchaseOrders.error}
                </div>
              )}
            </div>
          )}

          <Separator />

          {testResults.suppliers && (
            <div>
              <h4 className="font-medium flex items-center gap-2">
                Suppliers API
                {getStatusBadge(testResults.suppliers.status)}
              </h4>
              {testResults.suppliers.status === 'success' ? (
                <div className="text-sm text-muted-foreground">
                  Found {testResults.suppliers.count} suppliers
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Error: {testResults.suppliers.error}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Store State Results */}
      <Card>
        <CardHeader>
          <CardTitle>Store State Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium flex items-center gap-2">
              Purchase Orders Store
              {isLoadingPurchaseOrders ? (
                <Badge variant="outline">Loading...</Badge>
              ) : purchaseOrdersError ? (
                <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>
              ) : (
                <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Loaded</Badge>
              )}
            </h4>
            <div className="text-sm text-muted-foreground">
              {isLoadingPurchaseOrders ? (
                'Loading purchase orders...'
              ) : purchaseOrdersError ? (
                `Error: ${purchaseOrdersError}`
              ) : (
                `${purchaseOrders.length} purchase orders in store`
              )}
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="font-medium flex items-center gap-2">
              Suppliers Store
              {isLoadingSuppliers ? (
                <Badge variant="outline">Loading...</Badge>
              ) : suppliersError ? (
                <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>
              ) : (
                <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Loaded</Badge>
              )}
            </h4>
            <div className="text-sm text-muted-foreground">
              {isLoadingSuppliers ? (
                'Loading suppliers...'
              ) : suppliersError ? (
                `Error: ${suppliersError}`
              ) : (
                `${suppliers.length} suppliers in store`
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Preview */}
      {(purchaseOrders.length > 0 || suppliers.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Data Preview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {purchaseOrders.length > 0 && (
              <div>
                <h4 className="font-medium">Sample Purchase Orders:</h4>
                <div className="text-sm space-y-1">
                  {purchaseOrders.slice(0, 3).map((po, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded">
                      {po.orderNumber} - {po.supplier?.name || 'No supplier'} - {po.status}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {suppliers.length > 0 && (
              <div>
                <h4 className="font-medium">Sample Suppliers:</h4>
                <div className="text-sm space-y-1">
                  {suppliers.slice(0, 3).map((supplier, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded">
                      {supplier.name} - {supplier.email || 'No email'} - {supplier.status}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
