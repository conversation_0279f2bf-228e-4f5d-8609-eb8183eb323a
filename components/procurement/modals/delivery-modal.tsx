"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { DeliveryForm } from "@/components/procurement/forms/delivery-form"
import { 
  Truck, 
  Package, 
  MapPin, 
  Calendar,
  User,
  Phone,
  Mail,
  FileText,
  CheckCircle,
  Clock,
  AlertTriangle,
  Edit,
  Trash2,
  Eye,
  Download
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { format } from "date-fns"

import { Delivery } from '@/lib/stores/procurement-store'

// Use the Delivery type from the store
export type DeliveryItem = Delivery

interface DeliveryModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit' | 'view'
  delivery?: DeliveryItem
  onSubmit?: (data: any) => Promise<void>
  onDelete?: (id: string) => Promise<void>
  onStatusUpdate?: (id: string, status: string) => Promise<void>
  purchaseOrders?: Array<{ _id: string; orderNumber: string; supplier: { name: string } }>
  suppliers?: Array<{ _id: string; name: string }>
  isLoading?: boolean
}

export function DeliveryModal({
  isOpen,
  onClose,
  mode,
  delivery,
  onSubmit,
  onDelete,
  onStatusUpdate,
  purchaseOrders = [],
  suppliers = [],
  isLoading = false
}: DeliveryModalProps) {
  const [activeTab, setActiveTab] = useState("details")

  const handleSubmit = async (data: any) => {
    if (onSubmit) {
      await onSubmit(data)
      onClose()
    }
  }

  const handleDelete = async () => {
    if (delivery && onDelete && window.confirm('Are you sure you want to delete this delivery?')) {
      await onDelete(delivery._id)
      onClose()
    }
  }

  const handleStatusUpdate = async (newStatus: string) => {
    if (delivery && onStatusUpdate) {
      await onStatusUpdate(delivery._id, newStatus)
      toast({
        title: "Status Updated",
        description: `Delivery status updated to ${newStatus.replace('_', ' ')}`,
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      scheduled: 'outline',
      in_transit: 'secondary',
      delivered: 'default',
      partially_delivered: 'secondary',
      delayed: 'destructive',
      cancelled: 'destructive',
      returned: 'destructive'
    } as const

    const colors = {
      scheduled: 'text-blue-600',
      in_transit: 'text-orange-600',
      delivered: 'text-green-600',
      partially_delivered: 'text-yellow-600',
      delayed: 'text-red-600',
      cancelled: 'text-gray-600',
      returned: 'text-purple-600'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'outline',
      normal: 'secondary',
      high: 'destructive',
      urgent: 'destructive'
    } as const

    return (
      <Badge variant={variants[priority as keyof typeof variants] || 'outline'}>
        {priority.toUpperCase()}
      </Badge>
    )
  }

  const modalTitle = {
    create: 'Schedule New Delivery',
    edit: 'Edit Delivery',
    view: 'Delivery Details'
  }

  const modalDescription = {
    create: 'Schedule a new delivery for tracking and management',
    edit: 'Update delivery details and status information',
    view: 'View delivery details and manage status'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {modalTitle[mode]}
          </DialogTitle>
          <DialogDescription>
            {modalDescription[mode]}
          </DialogDescription>
        </DialogHeader>

        {mode === 'view' && delivery ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="items">Items</TabsTrigger>
              <TabsTrigger value="tracking">Tracking</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              {/* Header with actions */}
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold">{delivery.deliveryNumber}</h2>
                  <p className="text-muted-foreground">
                    PO: {delivery.purchaseOrder || delivery.purchaseOrderId || 'N/A'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleDelete}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              {/* Status and Priority */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Status:</span>
                  {getStatusBadge(delivery.status)}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Priority:</span>
                  {getPriorityBadge(delivery.priority)}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Type:</span>
                  <Badge variant="outline">{delivery.deliveryType.toUpperCase()}</Badge>
                </div>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      Supplier Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Supplier</label>
                      <p className="font-medium">{delivery.supplier.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email</label>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {delivery.supplier.email}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone</label>
                      <p className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {delivery.supplier.phone}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Delivery Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="font-medium">{delivery.deliveryAddress.street}</p>
                      <p>{delivery.deliveryAddress.city}</p>
                      {delivery.deliveryAddress.state && <p>{delivery.deliveryAddress.state}</p>}
                      {delivery.deliveryAddress.postalCode && <p>{delivery.deliveryAddress.postalCode}</p>}
                      <p>{delivery.deliveryAddress.country}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                    <p className="font-medium">{delivery.contactPerson}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Phone</label>
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {delivery.contactPhone}
                    </p>
                  </div>
                  {delivery.contactEmail && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email</label>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {delivery.contactEmail}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Dates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Schedule Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Expected Date</label>
                    <p className="font-medium">{format(new Date(delivery.expectedDate), 'PPP')}</p>
                  </div>
                  {delivery.promisedDate && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Promised Date</label>
                      <p className="font-medium">{format(new Date(delivery.promisedDate), 'PPP')}</p>
                    </div>
                  )}
                  {delivery.actualDate && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Actual Date</label>
                      <p className="font-medium">{format(new Date(delivery.actualDate), 'PPP')}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="items" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Items ({delivery.items.length})</CardTitle>
                  <CardDescription>
                    Items included in this delivery
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {delivery.items.map((item, index) => (
                      <div key={item._id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{item.itemDescription}</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">Ordered:</span>
                                <span className="ml-1 font-medium">{item.quantityOrdered}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Delivered:</span>
                                <span className="ml-1 font-medium">{item.quantityDelivered}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Unit Price:</span>
                                <span className="ml-1 font-medium">MWK {item.unitPrice.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Total:</span>
                                <span className="ml-1 font-medium">MWK {item.totalValue.toLocaleString()}</span>
                              </div>
                            </div>
                            {item.notes && (
                              <p className="text-sm text-muted-foreground mt-2">{item.notes}</p>
                            )}
                          </div>
                          <Badge variant={
                            item.condition === 'good' ? 'default' :
                            item.condition === 'damaged' ? 'destructive' :
                            item.condition === 'incomplete' ? 'destructive' :
                            'secondary'
                          }>
                            {item.condition.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Additional tabs would continue here... */}
          </Tabs>
        ) : (
          <DeliveryForm
            initialData={delivery ? {
              purchaseOrderId: delivery.purchaseOrderId || '',
              supplierId: delivery.supplierId || '',
              deliveryType: delivery.deliveryType,
              priority: delivery.priority,
              expectedDate: new Date(delivery.expectedDate),
              promisedDate: delivery.promisedDate ? new Date(delivery.promisedDate) : undefined,
              actualDate: delivery.actualDate ? new Date(delivery.actualDate) : undefined,
              trackingNumber: delivery.trackingNumber || '',
              carrier: delivery.carrier || '',
              shippingMethod: delivery.shippingMethod,
              shippingCost: delivery.shippingCost || 0,
              deliveryAddress: delivery.deliveryAddress,
              contactPerson: delivery.contactPerson,
              contactPhone: delivery.contactPhone,
              contactEmail: delivery.contactEmail || '',
              items: delivery.items.map(item => ({
                purchaseOrderItemId: item.purchaseOrderItemId,
                itemDescription: item.itemDescription,
                quantityOrdered: item.quantityOrdered,
                quantityDelivered: item.quantityDelivered,
                unitPrice: item.unitPrice,
                totalValue: item.totalValue,
                condition: item.condition === 'incomplete' ? 'poor' : item.condition as 'excellent' | 'good' | 'fair' | 'poor' | 'damaged',
                notes: item.notes || ''
              })),
              packingList: delivery.packingList || false,
              invoice: delivery.invoice || false,
              deliveryNote: delivery.deliveryNote || false,
              qualityCertificates: delivery.qualityCertificates || false,
              customsDocuments: delivery.customsDocuments || false,
              inspectionRequired: delivery.inspection?.inspectedBy ? true : false,
              inspectionNotes: delivery.inspection?.notes || '',
              specialInstructions: delivery.notes || '',
              internalNotes: delivery.notes || '',
            } : undefined}
            onSubmit={handleSubmit}
            isLoading={isLoading}
            purchaseOrders={purchaseOrders}
            suppliers={suppliers}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}
