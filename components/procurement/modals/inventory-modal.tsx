"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { InventoryForm, type InventoryFormData } from "@/components/procurement/forms/inventory-form"
import { StockMovementTracker } from "@/components/procurement/inventory/stock-movement-tracker"
import { 
  Package, 
  DollarSign, 
  MapPin, 
  AlertTriangle, 
  Calendar,
  Tag,
  FileText,
  Edit,
  Trash2,
  Plus,
  Minus,
  Eye
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Types for inventory item
export interface InventoryItem {
  _id: string
  inventoryId: string
  name: string
  description?: string
  category: {
    _id: string
    name: string
  }
  sku?: string
  barcode?: string
  currentStock: number
  minimumStock: number
  maximumStock?: number
  unit: string
  unitPrice: number
  totalValue: number
  currency: string
  lastPurchasePrice?: number
  location: string
  warehouse?: string
  shelf?: string
  zone?: string
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'on_order' | 'discontinued' | 'obsolete'
  reorderPoint: number
  reorderQuantity: number
  leadTime?: number
  qualityGrade?: 'A' | 'B' | 'C'
  expiryDate?: Date
  batchNumber?: string
  serialNumbers: string[]
  tags: string[]
  notes?: string
  preferredSuppliers: Array<{
    _id: string
    name: string
  }>
  createdAt: Date
  updatedAt: Date
}

interface InventoryModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit' | 'view'
  item?: InventoryItem
  onSubmit?: (data: InventoryFormData) => Promise<void>
  onDelete?: (id: string) => Promise<void>
  onStockUpdate?: (id: string, quantity: number, operation: 'add' | 'remove') => Promise<void>
  categories?: Array<{ _id: string; name: string }>
  suppliers?: Array<{ _id: string; name: string }>
  isLoading?: boolean
}

export function InventoryModal({
  isOpen,
  onClose,
  mode,
  item,
  onSubmit,
  onDelete,
  onStockUpdate,
  categories = [],
  suppliers = [],
  isLoading = false
}: InventoryModalProps) {
  const [stockQuantity, setStockQuantity] = useState(0)
  const [isStockLoading, setIsStockLoading] = useState(false)

  const handleSubmit = async (data: InventoryFormData) => {
    if (onSubmit) {
      await onSubmit(data)
      onClose()
    }
  }

  const handleDelete = async () => {
    if (item && onDelete && window.confirm('Are you sure you want to delete this inventory item?')) {
      await onDelete(item._id)
      onClose()
    }
  }

  const handleStockOperation = async (operation: 'add' | 'remove') => {
    if (item && onStockUpdate && stockQuantity > 0) {
      setIsStockLoading(true)
      try {
        await onStockUpdate(item._id, stockQuantity, operation)
        setStockQuantity(0)
        toast({
          title: "Stock Updated",
          description: `Successfully ${operation === 'add' ? 'added' : 'removed'} ${stockQuantity} ${item.unit}`,
        })
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to update stock",
          variant: "destructive",
        })
      } finally {
        setIsStockLoading(false)
      }
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      in_stock: 'default',
      low_stock: 'secondary',
      out_of_stock: 'destructive',
      on_order: 'secondary',
      discontinued: 'secondary',
      obsolete: 'destructive'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getQualityBadge = (grade?: string) => {
    if (!grade) return null
    
    const colors = {
      A: 'bg-green-100 text-green-800',
      B: 'bg-yellow-100 text-yellow-800',
      C: 'bg-red-100 text-red-800'
    }

    return (
      <Badge className={colors[grade as keyof typeof colors]}>
        Grade {grade}
      </Badge>
    )
  }

  const modalTitle = {
    create: 'Create New Inventory Item',
    edit: 'Edit Inventory Item',
    view: 'Inventory Item Details'
  }

  const modalDescription = {
    create: 'Add a new item to the procurement inventory',
    edit: 'Update inventory item details and stock information',
    view: 'View inventory item details and manage stock'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {modalTitle[mode]}
          </DialogTitle>
          <DialogDescription>
            {modalDescription[mode]}
          </DialogDescription>
        </DialogHeader>

        {mode === 'view' && item ? (
          <div className="space-y-6">
            {/* Header with actions */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">{item.name}</h2>
                <p className="text-muted-foreground">ID: {item.inventoryId}</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Category</label>
                    <p className="font-medium">{item.category.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <div className="mt-1">{getStatusBadge(item.status)}</div>
                  </div>
                  {item.sku && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">SKU</label>
                      <p className="font-medium">{item.sku}</p>
                    </div>
                  )}
                  {item.barcode && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Barcode</label>
                      <p className="font-medium">{item.barcode}</p>
                    </div>
                  )}
                </div>
                {item.description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="mt-1">{item.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stock Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Stock Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Current Stock</label>
                    <p className="text-2xl font-bold">{item.currentStock} {item.unit}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Minimum Stock</label>
                    <p className="font-medium">{item.minimumStock} {item.unit}</p>
                  </div>
                  {item.maximumStock && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Maximum Stock</label>
                      <p className="font-medium">{item.maximumStock} {item.unit}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Reorder Point</label>
                    <p className="font-medium">{item.reorderPoint} {item.unit}</p>
                  </div>
                </div>

                {/* Stock Management */}
                <Separator />
                <div className="space-y-4">
                  <h4 className="font-medium">Stock Management</h4>
                  <div className="flex items-center gap-4">
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={stockQuantity}
                      onChange={(e) => setStockQuantity(parseFloat(e.target.value) || 0)}
                      className="w-32 px-3 py-2 border rounded-md"
                      placeholder="Quantity"
                    />
                    <Button
                      onClick={() => handleStockOperation('add')}
                      disabled={stockQuantity <= 0 || isStockLoading}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Stock
                    </Button>
                    <Button
                      onClick={() => handleStockOperation('remove')}
                      disabled={stockQuantity <= 0 || isStockLoading}
                      variant="outline"
                      size="sm"
                    >
                      <Minus className="h-4 w-4 mr-2" />
                      Remove Stock
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Unit Price</label>
                    <p className="text-lg font-bold">{item.currency} {item.unitPrice.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Total Value</label>
                    <p className="text-lg font-bold">{item.currency} {item.totalValue.toLocaleString()}</p>
                  </div>
                  {item.lastPurchasePrice && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Purchase Price</label>
                      <p className="font-medium">{item.currency} {item.lastPurchasePrice.toLocaleString()}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Location Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location & Storage
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Location</label>
                    <p className="font-medium">{item.location}</p>
                  </div>
                  {item.warehouse && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Warehouse</label>
                      <p className="font-medium">{item.warehouse}</p>
                    </div>
                  )}
                  {item.shelf && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Shelf</label>
                      <p className="font-medium">{item.shelf}</p>
                    </div>
                  )}
                  {item.zone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Zone</label>
                      <p className="font-medium">{item.zone}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Stock Movement History */}
            <StockMovementTracker
              inventoryId={item._id}
              showFilters={false}
              maxHeight="300px"
            />

            {/* Additional Information */}
            {(item.tags.length > 0 || item.notes || item.qualityGrade) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Additional Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {item.qualityGrade && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Quality Grade</label>
                      <div className="mt-1">{getQualityBadge(item.qualityGrade)}</div>
                    </div>
                  )}
                  {item.tags.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Tags</label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {item.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {item.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Notes</label>
                      <p className="mt-1 text-sm">{item.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <InventoryForm
            initialData={item ? {
              name: item.name,
              description: item.description,
              category: item.category._id,
              sku: item.sku,
              barcode: item.barcode,
              currentStock: item.currentStock,
              minimumStock: item.minimumStock,
              maximumStock: item.maximumStock,
              unit: item.unit,
              unitPrice: item.unitPrice,
              currency: item.currency,
              lastPurchasePrice: item.lastPurchasePrice,
              location: item.location,
              warehouse: item.warehouse,
              shelf: item.shelf,
              zone: item.zone,
              status: item.status,
              reorderPoint: item.reorderPoint,
              reorderQuantity: item.reorderQuantity,
              leadTime: item.leadTime,
              qualityGrade: item.qualityGrade,
              expiryDate: item.expiryDate ? item.expiryDate.toISOString().split('T')[0] : '',
              batchNumber: item.batchNumber,
              serialNumbers: item.serialNumbers,
              tags: item.tags,
              notes: item.notes,
              preferredSuppliers: item.preferredSuppliers.map(s => s._id),
            } : undefined}
            onSubmit={handleSubmit}
            onCancel={onClose}
            isLoading={isLoading}
            categories={categories}
            suppliers={suppliers}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}
