// components/procurement/inventory-management.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { InventoryList } from "@/components/procurement/lists/inventory-list"
import { ReorderSuggestions } from "@/components/procurement/inventory/reorder-suggestions"
import { useProcurementStore } from "@/lib/stores/procurement-store"
import {
  PlusCircle,
  Search,
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Download,
  RefreshCw
} from "lucide-react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { toast } from "@/components/ui/use-toast"

export function InventoryManagement() {
  const {
    inventory: inventoryItems,
    categories,
    suppliers,
    isLoadingInventory,
    inventoryError,
    stockLevelReport,
    fetchInventory: fetchInventoryItems,
    fetchCategories,
    fetchSuppliers,
    createInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    updateStock: updateInventoryStock,
    fetchStockLevelReport: getStockLevelReport,
    clearInventoryError
  } = useProcurementStore()

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          fetchInventoryItems(),
          fetchCategories(),
          fetchSuppliers(),
          getStockLevelReport()
        ])
      } catch (error) {
        console.error('Error loading inventory data:', error)
      }
    }

    loadData()
  }, [fetchInventoryItems, fetchCategories, fetchSuppliers, getStockLevelReport])

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearInventoryError()
    }
  }, [clearInventoryError])

  const handleRefresh = async () => {
    try {
      await Promise.all([
        fetchInventoryItems(),
        getStockLevelReport()
      ])
      toast({
        title: "Refreshed",
        description: "Inventory data has been refreshed",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh inventory data",
        variant: "destructive",
      })
    }
  }

  const handleCreateItem = async (data: any) => {
    await createInventoryItem(data)
  }

  const handleUpdateItem = async (id: string, data: any) => {
    await updateInventoryItem(id, data)
  }

  const handleDeleteItem = async (id: string) => {
    await deleteInventoryItem(id)
  }

  const handleStockUpdate = async (id: string, quantity: number, operation: 'add' | 'remove') => {
    await updateInventoryStock(id, quantity, operation)
  }

  // Calculate summary statistics with null safety
  const totalItems = inventoryItems?.length || 0
  const lowStockItems = inventoryItems?.filter(item => item.currentStock <= item.minimumStock && item.currentStock > 0).length || 0
  const outOfStockItems = inventoryItems?.filter(item => item.currentStock === 0).length || 0
  const totalValue = inventoryItems?.reduce((sum, item) => sum + (item.totalValue || 0), 0) || 0

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Inventory Management"
        text="Track inventory levels, stock movements, and asset management"
      >
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoadingInventory}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingInventory ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </DashboardHeader>

      <div className="space-y-6">
        {/* Error Display */}
        {inventoryError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{inventoryError}</AlertDescription>
          </Alert>
        )}

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stockLevelReport?.totalItems || totalItems}</div>
              <p className="text-xs text-muted-foreground">
                Tracked inventory items
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stockLevelReport?.lowStock || lowStockItems}</div>
              <p className="text-xs text-muted-foreground">
                Need restocking
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stockLevelReport?.outOfStock || outOfStockItems}</div>
              <p className="text-xs text-muted-foreground">
                Urgent restocking needed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">MWK {(stockLevelReport?.totalValue || totalValue).toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Current inventory value
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Reorder Suggestions */}
        {(stockLevelReport?.lowStock || 0) > 0 && (
          <ReorderSuggestions
            onCreatePurchaseOrder={(items) => {
              // Handle purchase order creation
              console.log('Creating purchase order for:', items);
              // You can integrate this with your purchase order creation flow
            }}
          />
        )}

        {/* Inventory List Component */}
        <InventoryList
          items={inventoryItems || []}
          isLoading={isLoadingInventory}
          onRefresh={handleRefresh}
          onCreateItem={handleCreateItem}
          onUpdateItem={handleUpdateItem}
          onDeleteItem={handleDeleteItem}
          onStockUpdate={handleStockUpdate}
          categories={categories || []}
          suppliers={suppliers || []}
        />
      </div>
    </DashboardShell>
  )
}
