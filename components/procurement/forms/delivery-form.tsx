"use client"

import { useState, useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Plus, X, Truck, Package, MapPin, Clock } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"

// Delivery item schema
const deliveryItemSchema = z.object({
  purchaseOrderItemId: z.string().min(1, "Purchase order item is required"),
  itemDescription: z.string().min(1, "Item description is required"),
  quantityOrdered: z.number().min(1, "Quantity ordered must be positive"),
  quantityDelivered: z.number().min(0, "Quantity delivered must be non-negative"),
  unitPrice: z.number().min(0, "Unit price must be non-negative"),
  totalValue: z.number().min(0, "Total value must be non-negative"),
  condition: z.enum(['excellent', 'good', 'fair', 'poor', 'damaged']).default('good'),
  notes: z.string().optional(),
})

// Main delivery form schema
const deliveryFormSchema = z.object({
  // Basic Information
  purchaseOrderId: z.string().min(1, "Purchase order is required"),
  supplierId: z.string().min(1, "Supplier is required"),
  contractId: z.string().optional(),
  
  // Delivery Details
  deliveryType: z.enum(['full', 'partial', 'split', 'emergency']).default('full'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  expectedDate: z.date({ required_error: "Expected delivery date is required" }),
  promisedDate: z.date().optional(),
  actualDate: z.date().optional(),
  
  // Shipping Information
  trackingNumber: z.string().optional(),
  carrier: z.string().optional(),
  shippingMethod: z.enum(['standard', 'express', 'overnight', 'pickup', 'direct']).default('standard'),
  shippingCost: z.number().min(0, "Shipping cost must be non-negative").optional(),
  
  // Delivery Address
  deliveryAddress: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().min(1, "Country is required"),
  }),
  
  // Contact Information
  contactPerson: z.string().min(1, "Contact person is required"),
  contactPhone: z.string().min(1, "Contact phone is required"),
  contactEmail: z.string().email("Valid email is required").optional(),
  
  // Items
  items: z.array(deliveryItemSchema).min(1, "At least one item is required"),
  
  // Documentation
  packingList: z.boolean().default(false),
  invoice: z.boolean().default(false),
  deliveryNote: z.boolean().default(false),
  qualityCertificates: z.boolean().default(false),
  customsDocuments: z.boolean().default(false),
  
  // Quality Inspection
  inspectionRequired: z.boolean().default(true),
  inspectionNotes: z.string().optional(),
  
  // Additional Information
  specialInstructions: z.string().optional(),
  internalNotes: z.string().optional(),
})

type DeliveryFormData = z.infer<typeof deliveryFormSchema>

interface DeliveryFormProps {
  initialData?: Partial<DeliveryFormData>
  onSubmit: (data: DeliveryFormData) => Promise<void>
  isLoading?: boolean
  purchaseOrders?: Array<{ _id: string; orderNumber: string; supplier: { name: string } }>
  suppliers?: Array<{ _id: string; name: string }>
}

const deliveryTypes = [
  { value: 'full', label: 'Full Delivery' },
  { value: 'partial', label: 'Partial Delivery' },
  { value: 'split', label: 'Split Delivery' },
  { value: 'emergency', label: 'Emergency Delivery' },
]

const priorities = [
  { value: 'low', label: 'Low Priority' },
  { value: 'normal', label: 'Normal Priority' },
  { value: 'high', label: 'High Priority' },
  { value: 'urgent', label: 'Urgent' },
]

const shippingMethods = [
  { value: 'standard', label: 'Standard Shipping' },
  { value: 'express', label: 'Express Shipping' },
  { value: 'overnight', label: 'Overnight Delivery' },
  { value: 'pickup', label: 'Customer Pickup' },
  { value: 'direct', label: 'Direct Delivery' },
]

const itemConditions = [
  { value: 'excellent', label: 'Excellent' },
  { value: 'good', label: 'Good' },
  { value: 'fair', label: 'Fair' },
  { value: 'poor', label: 'Poor' },
  { value: 'damaged', label: 'Damaged' },
]

export function DeliveryForm({
  initialData,
  onSubmit,
  isLoading = false,
  purchaseOrders = [],
  suppliers = []
}: DeliveryFormProps) {
  const [selectedPO, setSelectedPO] = useState<any>(null);

  // Debug logging to check data availability
  useEffect(() => {
    console.log('DeliveryForm - Purchase Orders:', purchaseOrders);
    console.log('DeliveryForm - Suppliers:', suppliers);
  }, [purchaseOrders, suppliers]);

  const form = useForm({
    resolver: zodResolver(deliveryFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      purchaseOrderId: '',
      supplierId: '',
      contractId: '',
      deliveryType: 'full',
      priority: 'normal',
      expectedDate: new Date(),
      promisedDate: undefined,
      actualDate: undefined,
      trackingNumber: '',
      carrier: '',
      shippingMethod: 'standard',
      shippingCost: 0,
      deliveryAddress: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'Malawi',
      },
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
      items: [{
        purchaseOrderItemId: '',
        itemDescription: '',
        quantityOrdered: 1,
        quantityDelivered: 0,
        unitPrice: 0,
        totalValue: 0,
        condition: 'good',
        notes: '',
      }],
      packingList: false,
      invoice: false,
      deliveryNote: false,
      qualityCertificates: false,
      customsDocuments: false,
      inspectionRequired: true,
      inspectionNotes: '',
      specialInstructions: '',
      internalNotes: '',
      ...initialData
    }
  }) as ReturnType<typeof useForm<DeliveryFormData>>

  // Handle Purchase Order selection
  const handlePOSelection = (poId: string) => {
    const po = purchaseOrders.find(p => p._id === poId);
    if (po) {
      setSelectedPO(po);
      // Auto-populate supplier
      form.setValue('supplierId', po.supplier._id || po.supplier.id);

      // Auto-populate items if available
      if (po.items && po.items.length > 0) {
        const items = po.items.map((item: any) => ({
          purchaseOrderItemId: item._id || item.id,
          itemDescription: item.description || item.name || item.itemDescription,
          quantityOrdered: item.quantity || item.quantityOrdered || 1,
          quantityDelivered: item.quantity || item.quantityOrdered || 1,
          unitPrice: item.unitPrice || item.price || 0,
          totalValue: (item.quantity || 1) * (item.unitPrice || item.price || 0),
          condition: 'good',
          notes: '',
        }));
        form.setValue('items', items);
      }

      // Auto-populate delivery address if available
      if (po.deliveryAddress) {
        form.setValue('deliveryAddress', po.deliveryAddress);
      }

      toast({
        title: "Purchase Order Selected",
        description: `Selected PO ${po.orderNumber} from ${po.supplier.name}`,
      });
    }
  };

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items"
  })

  const handleSubmit = async (data: DeliveryFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: "Delivery has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save delivery. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Calculate total value for an item
  const calculateItemTotal = (index: number) => {
    const quantity = form.watch(`items.${index}.quantityDelivered`)
    const price = form.watch(`items.${index}.unitPrice`)
    const total = quantity * price
    form.setValue(`items.${index}.totalValue`, total)
  }

  // Add new item
  const addItem = () => {
    append({
      purchaseOrderItemId: '',
      itemDescription: '',
      quantityOrdered: 1,
      quantityDelivered: 0,
      unitPrice: 0,
      totalValue: 0,
      condition: 'good',
      notes: '',
    })
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5" />
          {initialData ? 'Edit Delivery' : 'Create New Delivery'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update delivery details and tracking information' : 'Create a new delivery record for tracking goods receipt'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Package className="h-5 w-5" />
                Basic Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="purchaseOrderId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purchase Order *</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          handlePOSelection(value);
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={
                              purchaseOrders.length === 0
                                ? "Loading purchase orders..."
                                : "Select purchase order"
                            } />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {purchaseOrders.length === 0 ? (
                            <SelectItem value="no-data" disabled>
                              No purchase orders available
                            </SelectItem>
                          ) : (
                            purchaseOrders.map((po) => (
                              <SelectItem key={po._id} value={po._id}>
                                {po.orderNumber} - {po.supplier?.name || 'Unknown Supplier'}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                      {purchaseOrders.length === 0 && (
                        <FormDescription className="text-amber-600">
                          No purchase orders found. Please create a purchase order first.
                        </FormDescription>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supplierId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={
                              suppliers.length === 0
                                ? "Loading suppliers..."
                                : selectedPO
                                  ? `${selectedPO.supplier?.name || 'Auto-selected from PO'}`
                                  : "Select supplier"
                            } />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {suppliers.length === 0 ? (
                            <SelectItem value="no-data" disabled>
                              No suppliers available
                            </SelectItem>
                          ) : (
                            suppliers.map((supplier) => (
                              <SelectItem key={supplier._id} value={supplier._id}>
                                {supplier.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                      {suppliers.length === 0 && (
                        <FormDescription className="text-amber-600">
                          No suppliers found. Please add suppliers first.
                        </FormDescription>
                      )}
                      {selectedPO && (
                        <FormDescription className="text-green-600">
                          Auto-selected from Purchase Order: {selectedPO.supplier?.name}
                        </FormDescription>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="deliveryType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Delivery Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {deliveryTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {priorities.map((priority) => (
                            <SelectItem key={priority.value} value={priority.value}>
                              {priority.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shippingMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shipping Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {shippingMethods.map((method) => (
                            <SelectItem key={method.value} value={method.value}>
                              {method.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Dates Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Delivery Schedule
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="expectedDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Expected Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="promisedDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Promised Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="actualDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Actual Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Shipping Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Shipping Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="trackingNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tracking Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter tracking number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="carrier"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Carrier</FormLabel>
                      <FormControl>
                        <Input placeholder="Shipping carrier" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shippingCost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shipping Cost</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Delivery Address */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Delivery Address
              </h3>

              <FormField
                control={form.control}
                name="deliveryAddress.street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter street address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <FormField
                  control={form.control}
                  name="deliveryAddress.city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City *</FormLabel>
                      <FormControl>
                        <Input placeholder="City" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliveryAddress.state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Region</FormLabel>
                      <FormControl>
                        <Input placeholder="State or region" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliveryAddress.postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Postal code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliveryAddress.country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country *</FormLabel>
                      <FormControl>
                        <Input placeholder="Country" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Contact Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="contactPerson"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Person *</FormLabel>
                      <FormControl>
                        <Input placeholder="Contact person name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Phone *</FormLabel>
                      <FormControl>
                        <Input placeholder="Phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Delivery Items */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Delivery Items</h3>
                <Button type="button" onClick={addItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>

              {fields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <FormField
                      control={form.control}
                      name={`items.${index}.itemDescription`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Item Description *</FormLabel>
                          <FormControl>
                            <Input placeholder="Describe the item" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`items.${index}.condition`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Condition</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {itemConditions.map((condition) => (
                                <SelectItem key={condition.value} value={condition.value}>
                                  {condition.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <FormField
                      control={form.control}
                      name={`items.${index}.quantityOrdered`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Qty Ordered</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`items.${index}.quantityDelivered`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Qty Delivered</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(parseInt(e.target.value) || 0)
                                calculateItemTotal(index)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`items.${index}.unitPrice`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unit Price</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              {...field}
                              onChange={(e) => {
                                field.onChange(parseFloat(e.target.value) || 0)
                                calculateItemTotal(index)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`items.${index}.totalValue`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Total Value</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              readOnly
                              className="bg-muted"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name={`items.${index}.notes`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Item Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Any notes about this item"
                            className="min-h-[60px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Card>
              ))}
            </div>

            {/* Documentation */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Documentation</h3>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <FormField
                  control={form.control}
                  name="packingList"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Packing List</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="invoice"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Invoice</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliveryNote"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Delivery Note</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="qualityCertificates"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Quality Certificates</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="customsDocuments"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Customs Documents</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Quality Inspection */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Quality Inspection</h3>

              <FormField
                control={form.control}
                name="inspectionRequired"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Quality Inspection Required</FormLabel>
                      <FormDescription>
                        Check if this delivery requires quality inspection before acceptance
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {form.watch("inspectionRequired") && (
                <FormField
                  control={form.control}
                  name="inspectionNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Inspection Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Quality inspection requirements and notes"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Additional Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="specialInstructions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Special Instructions</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Special handling or delivery instructions"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="internalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Internal Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes for team reference"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex justify-end pt-6">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : initialData ? "Update Delivery" : "Create Delivery"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
