"use client"

import { useState } from "react"
import { use<PERSON><PERSON>, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CalendarIcon, Plus, X, FileText, AlertCircle, Calculator, Clock } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"

// Requisition item schema
const requisitionItemSchema = z.object({
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unit: z.string().min(1, "Unit is required"),
  estimatedUnitPrice: z.number().min(0, "Price must be non-negative").optional(),
  totalPrice: z.number().min(0, "Total price must be non-negative").optional(),
  category: z.string().optional(),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
  notes: z.string().optional(),
})

// Main requisition form schema
const requisitionFormSchema = z.object({
  // Basic Information
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().optional(),
  requisitionId: z.string().min(1, "Requisition ID is required"),
  
  // Request Details
  requestedBy: z.string().min(1, "Requested by is required"),
  departmentId: z.string().min(1, "Department is required"),
  date: z.date({ required_error: "Request date is required" }),
  requiredDate: z.date({ required_error: "Required date is required" }),
  
  // Priority and Status
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  status: z.enum(['draft', 'submitted', 'approved', 'rejected', 'ordered', 'completed', 'cancelled']).default('draft'),
  
  // Items
  items: z.array(requisitionItemSchema).min(1, "At least one item is required"),
  
  // Financial
  totalAmount: z.number().min(0, "Total amount must be non-negative").optional(),
  budgetId: z.string().optional(),
  currency: z.string().length(3, "Currency must be 3 characters").default("MWK"),
  
  // Purpose and Justification
  purpose: z.string().min(1, "Purpose is required"),
  justification: z.string().optional(),
  businessCase: z.string().optional(),
  
  // Additional Information
  notes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
}).refine((data) => data.requiredDate >= data.date, {
  message: "Required date must be on or after request date",
  path: ["requiredDate"],
})

type RequisitionFormData = z.infer<typeof requisitionFormSchema>

interface RequisitionFormProps {
  initialData?: Partial<RequisitionFormData>
  onSubmit: (data: RequisitionFormData) => Promise<void>
  isLoading?: boolean
  departments?: Array<{ _id: string; name: string }>
  budgets?: Array<{ _id: string; name: string; fiscalYear: string }>
  categories?: Array<{ value: string; label: string }>
  currentUser?: { _id: string; name: string; department?: string }
}

const priorities = [
  { value: 'low', label: 'Low Priority', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Medium Priority', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'High Priority', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' },
]

const urgencyLevels = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
]

const units = [
  'pieces', 'units', 'boxes', 'packages', 'sets', 'pairs',
  'meters', 'liters', 'kilograms', 'hours', 'days', 'months'
]

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
]

export function RequisitionForm({ 
  initialData, 
  onSubmit, 
  isLoading = false, 
  departments = [],
  budgets = [],
  categories = [],
  currentUser
}: RequisitionFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [newTag, setNewTag] = useState("")

  const form = useForm({
    resolver: zodResolver(requisitionFormSchema),
    mode: "onChange" as const,
    defaultValues: {
      title: '',
      description: '',
      requisitionId: '',
      requestedBy: currentUser?._id || '',
      departmentId: currentUser?.department || '',
      date: new Date(),
      requiredDate: new Date(new Date().setDate(new Date().getDate() + 7)),
      priority: 'medium',
      status: 'draft',
      items: [{
        name: '',
        description: '',
        quantity: 1,
        unit: 'pieces',
        estimatedUnitPrice: 0,
        totalPrice: 0,
        category: '',
        urgency: 'medium',
        notes: '',
      }],
      totalAmount: 0,
      budgetId: '',
      currency: 'MWK',
      purpose: '',
      justification: '',
      businessCase: '',
      notes: '',
      attachments: [],
      tags: [],
      ...initialData
    }
  }) as ReturnType<typeof useForm<RequisitionFormData>>

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items"
  })

  const handleSubmit = async (data: RequisitionFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: "Requisition has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save requisition. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Generate requisition ID
  const generateRequisitionId = () => {
    const dept = departments.find(d => d._id === form.getValues("departmentId"))?.name || "DEPT"
    const id = `REQ-${dept.substring(0, 3).toUpperCase()}-${Date.now().toString().slice(-6)}`
    form.setValue("requisitionId", id)
  }

  // Calculate item total
  const calculateItemTotal = (index: number) => {
    const quantity = form.watch(`items.${index}.quantity`)
    const price = form.watch(`items.${index}.estimatedUnitPrice`) || 0
    const total = quantity * price
    form.setValue(`items.${index}.totalPrice`, total)
    calculateGrandTotal()
  }

  // Calculate grand total
  const calculateGrandTotal = () => {
    const items = form.getValues("items")
    const total = items.reduce((sum, item) => sum + (item.totalPrice || 0), 0)
    form.setValue("totalAmount", total)
  }

  // Add new item
  const addItem = () => {
    append({
      name: '',
      description: '',
      quantity: 1,
      unit: 'pieces',
      estimatedUnitPrice: 0,
      totalPrice: 0,
      category: '',
      urgency: 'medium',
      notes: '',
    })
  }

  // Add tag
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues("tags") || []
    if (!currentTags.includes(newTag.trim())) {
        form.setValue("tags", [...currentTags, newTag.trim()])
      }
      setNewTag("")
    }
  }

  // Remove tag
  const removeTag = (index: number) => {
    const currentTags = form.getValues("tags")
    form.setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {initialData ? 'Edit Requisition' : 'Create New Requisition'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update requisition details and items' : 'Create a new purchase requisition for approval'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="items" className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Items
                </TabsTrigger>
                <TabsTrigger value="additional" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Additional
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Requisition Title *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter requisition title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requisitionId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Requisition ID *</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input placeholder="REQ-XXX-XXXXXX" {...field} />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={generateRequisitionId}
                          >
                            Generate
                          </Button>
                        </div>
                        <FormDescription>
                          Unique identifier for this requisition
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Brief description of the requisition"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="departmentId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {departments.map((dept) => (
                              <SelectItem key={dept._id} value={dept._id}>
                                {dept.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {priorities.map((priority) => (
                              <SelectItem key={priority.value} value={priority.value}>
                                <div className="flex items-center gap-2">
                                  <Badge className={priority.color}>
                                    {priority.label}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Request Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requiredDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Required Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription>
                          When do you need these items?
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purpose *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Explain the purpose of this requisition"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="budgetId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select budget" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="no-budget">No Budget Link</SelectItem>
                            {budgets.map((budget) => (
                              <SelectItem key={budget._id} value={budget._id}>
                                {budget.name} ({budget.fiscalYear})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.value} value={currency.value}>
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Items Tab */}
              <TabsContent value="items" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Requisition Items
                  </h3>
                  <Button type="button" onClick={addItem} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Item Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter item name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.category`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="no-category">No Category</SelectItem>
                                {categories.map((category) => (
                                  <SelectItem key={category.value} value={category.value}>
                                    {category.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`items.${index}.description`}
                      render={({ field }) => (
                        <FormItem className="mb-4">
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Detailed description of the item"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Quantity *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseInt(e.target.value) || 1)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.unit`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unit *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {units.map((unit) => (
                                  <SelectItem key={unit} value={unit}>
                                    {unit}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.estimatedUnitPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unit Price</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(parseFloat(e.target.value) || 0)
                                  calculateItemTotal(index)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.totalPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total Price</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                readOnly
                                className="bg-muted"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.urgency`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Urgency</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {urgencyLevels.map((urgency) => (
                                  <SelectItem key={urgency.value} value={urgency.value}>
                                    {urgency.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`items.${index}.notes`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Item Notes</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any specific notes for this item"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </Card>
                ))}

                {/* Total Summary */}
                <Card className="p-4 bg-muted/50">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium">Total Amount:</span>
                    <span className="text-xl font-bold">
                      {form.watch("currency")} {form.watch("totalAmount")?.toLocaleString() || '0.00'}
                    </span>
                  </div>
                </Card>
              </TabsContent>

              {/* Additional Information Tab */}
              <TabsContent value="additional" className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Additional Information
                </h3>

                <FormField
                  control={form.control}
                  name="justification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Justification</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Provide justification for this requisition"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Explain why these items are needed and how they support business objectives
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="businessCase"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Case</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Provide business case for this requisition"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Detail the business impact and expected benefits
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Tags */}
                <div className="space-y-3">
                  <FormLabel>Tags</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add tags for categorization"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {(form.watch("tags") || []).map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => removeTag(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes or comments"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const tabs = ["basic", "items", "additional"]
                  const currentIndex = tabs.indexOf(activeTab)
                  if (currentIndex > 0) {
                    setActiveTab(tabs[currentIndex - 1])
                  }
                }}
                disabled={activeTab === "basic"}
              >
                Previous
              </Button>
              
              {activeTab === "additional" ? (
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    disabled={isLoading}
                    onClick={async () => {
                      console.log('Save as Draft clicked')
                      form.setValue("status", "draft")

                      // Trigger form validation and submission
                      const isValid = await form.trigger()
                      if (isValid) {
                        const formData = form.getValues()
                        console.log('Form data for draft:', formData)
                        await handleSubmit(formData)
                      } else {
                        console.log('Form validation failed for draft')
                        toast({
                          title: "Validation Error",
                          description: "Please fill in all required fields before saving.",
                          variant: "destructive",
                        })
                      }
                    }}
                  >
                    {isLoading ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="button"
                    disabled={isLoading}
                    onClick={async () => {
                      console.log('Submit for Approval clicked')
                      form.setValue("status", "submitted")

                      // Trigger form validation and submission
                      const isValid = await form.trigger()
                      if (isValid) {
                        const formData = form.getValues()
                        console.log('Form data for submission:', formData)
                        await handleSubmit(formData)
                      } else {
                        console.log('Form validation failed for submission')
                        toast({
                          title: "Validation Error",
                          description: "Please fill in all required fields before submitting.",
                          variant: "destructive",
                        })
                      }
                    }}
                  >
                    {isLoading ? "Submitting..." : "Submit for Approval"}
                  </Button>
                </div>
              ) : (
                <Button
                  type="button"
                  onClick={() => {
                    const tabs = ["basic", "items", "additional"]
                    const currentIndex = tabs.indexOf(activeTab)
                    if (currentIndex < tabs.length - 1) {
                      setActiveTab(tabs[currentIndex + 1])
                    }
                  }}
                >
                  Next
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
