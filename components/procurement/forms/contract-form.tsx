'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CalendarIcon, Plus, X, FileText, CheckCircle, AlertCircle, Circle, Shield } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

// Performance metric schema
const performanceMetricSchema = z.object({
  metric: z.string().min(1, 'Metric name is required'),
  target: z.string().min(1, 'Target value is required'),
  measurement: z.string().min(1, 'Measurement method is required'),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'annually']),
});

// Contract form schema
const contractFormSchema = z.object({
  // Basic Information
  title: z.string().min(1, 'Contract title is required'),
  contractNumber: z.string().min(1, 'Contract number is required'),
  description: z.string().min(1, 'Description is required'),
  supplierId: z.string().min(1, 'Supplier is required'),
  contractType: z.enum(['service', 'supply', 'maintenance', 'lease', 'consulting', 'construction']),

  // Financial Details
  value: z.number().min(0, 'Contract value must be positive'),
  currency: z.string().min(1, 'Currency is required'),
  budgetCategory: z.string().optional(),
  costCenter: z.string().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountRate: z.number().min(0).max(100).optional(),

  // Dates
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date({ required_error: 'End date is required' }),
  renewalDate: z.date().optional(),
  autoRenewal: z.boolean().default(false),
  renewalTerms: z.string().optional(),

  // Terms and Conditions
  terms: z.array(z.string()).default([]),
  paymentTerms: z.string().min(1, 'Payment terms are required'),
  deliveryTerms: z.string().optional(),
  penaltyClause: z.string().optional(),
  warrantyTerms: z.string().optional(),

  // Performance Metrics
  performanceMetrics: z.array(performanceMetricSchema).default([]),

  // Compliance and Legal
  complianceRequirements: z.array(z.string()).default([]),
  legalReviewRequired: z.boolean().default(false),

  // Notifications
  renewalReminder: z.boolean().default(true),
  expiryReminder: z.boolean().default(true),
  performanceReview: z.boolean().default(false),
  reminderDays: z.array(z.number()).default([30, 7]),

  // Additional Information
  priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  riskLevel: z.enum(['low', 'medium', 'high']).default('medium'),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  status: z.enum(['draft', 'active', 'expired', 'terminated', 'renewed', 'suspended', 'pending_approval']).default('draft'),
}).refine((data) => data.endDate >= data.startDate, {
  message: 'End date must be on or after start date',
  path: ['endDate'],
}).refine((data) => !data.renewalDate || data.renewalDate >= data.endDate, {
  message: 'Renewal date must be on or after end date',
  path: ['renewalDate'],
});

// Step validation schemas
const basicInfoSchema = z.object({
  title: z.string().min(1, 'Contract title is required'),
  contractNumber: z.string().min(1, 'Contract number is required'),
  description: z.string().min(1, 'Description is required'),
  supplierId: z.string().min(1, 'Supplier is required'),
  contractType: z.enum(['service', 'supply', 'maintenance', 'lease', 'consulting', 'construction']),
});

const financialSchema = z.object({
  value: z.number().min(0, 'Contract value must be positive'),
  currency: z.string().min(1, 'Currency is required'),
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date({ required_error: 'End date is required' }),
}).refine((data) => data.endDate >= data.startDate, {
  message: 'End date must be on or after start date',
  path: ['endDate'],
});

const termsSchema = z.object({
  paymentTerms: z.string().min(1, 'Payment terms are required'),
});

type ContractFormData = z.infer<typeof contractFormSchema>;

// Step completion status type
type StepStatus = 'incomplete' | 'valid' | 'invalid';

interface StepValidation {
  basic: StepStatus;
  financial: StepStatus;
  terms: StepStatus;
  performance: StepStatus;
  compliance: StepStatus;
}

interface ContractFormProps {
  initialData?: Partial<ContractFormData>;
  onSubmit: (data: ContractFormData) => Promise<void>;
  isLoading?: boolean;
  suppliers?: Array<{ _id: string; name: string; email: string }>;
  budgetCategories?: Array<{ _id: string; name: string; description?: string }>;
  costCenters?: Array<{ _id: string; name: string }>;
}

const contractTypes = [
  { value: 'service', label: 'Service Contract' },
  { value: 'supply', label: 'Supply Contract' },
  { value: 'maintenance', label: 'Maintenance Contract' },
  { value: 'lease', label: 'Lease Agreement' },
  { value: 'consulting', label: 'Consulting Contract' },
  { value: 'construction', label: 'Construction Contract' },
];

const currencies = [
  { value: 'MWK', label: 'Malawian Kwacha (MWK)' },
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
];

const frequencies = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'annually', label: 'Annually' },
];

export function ContractForm({
  initialData,
  onSubmit,
  isLoading = false,
  suppliers = [],
  budgetCategories = [],
  costCenters = []
}: ContractFormProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [stepValidation, setStepValidation] = useState<StepValidation>({
    basic: 'incomplete',
    financial: 'incomplete',
    terms: 'incomplete',
    performance: 'incomplete',
    compliance: 'incomplete'
  });
  const [attemptedSteps, setAttemptedSteps] = useState<Set<string>>(new Set(['basic']));

  // Debug logging for budget categories
  useEffect(() => {
    console.log('ContractForm - Budget Categories:', budgetCategories);
    console.log('ContractForm - Suppliers:', suppliers);
    console.log('ContractForm - Cost Centers:', costCenters);
  }, [budgetCategories, suppliers, costCenters]);

  const form = useForm<ContractFormData>({
    resolver: zodResolver(contractFormSchema),
    defaultValues: {
      contractNumber: '',
      title: '',
      description: '',
      contractType: 'service',
      supplierId: '',
      value: 0,
      currency: 'MWK',
      startDate: new Date(),
      endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      autoRenewal: false,
      renewalTerms: '',
      paymentTerms: '',
      deliveryTerms: '',
      penaltyClause: '',
      warrantyTerms: '',
      terms: [],
      performanceMetrics: [],
      complianceRequirements: [],
      legalReviewRequired: false,
      renewalReminder: true,
      expiryReminder: true,
      performanceReview: false,
      reminderDays: [30, 7],
      priority: 'medium',
      riskLevel: 'medium',
      status: 'draft',
      tags: [],
      notes: '',
      ...initialData,
    },
  });

  const { fields: termsFields, append: appendTerm, remove: removeTerm } = useFieldArray({
    control: form.control,
    name: 'terms'
  });

  const { fields: metricsFields, append: appendMetric, remove: removeMetric } = useFieldArray({
    control: form.control,
    name: 'performanceMetrics'
  });

  const { fields: complianceFields, append: appendCompliance, remove: removeCompliance } = useFieldArray({
    control: form.control,
    name: 'complianceRequirements'
  });

  const { fields: tagsFields, append: appendTag, remove: removeTag } = useFieldArray({
    control: form.control,
    name: 'tags'
  });

  // State for input fields
  const [newTerm, setNewTerm] = useState('');
  const [newTag, setNewTag] = useState('');
  const [newCompliance, setNewCompliance] = useState('');

  // Helper functions for dynamic fields
  const addTerm = () => {
    if (newTerm.trim()) {
      appendTerm(newTerm.trim());
      setNewTerm('');
    }
  };

  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues('tags') || [];
      if (!currentTags.includes(newTag.trim())) {
        appendTag(newTag.trim());
      }
      setNewTag('');
    }
  };

  const addCompliance = () => {
    if (newCompliance.trim()) {
      const currentCompliance = form.getValues('complianceRequirements') || [];
      if (!currentCompliance.includes(newCompliance.trim())) {
        appendCompliance(newCompliance.trim());
      }
      setNewCompliance('');
    }
  };

  // Step validation functions
  const validateStep = async (step: string): Promise<boolean> => {
    const formData = form.getValues();

    try {
      switch (step) {
        case 'basic':
          await basicInfoSchema.parseAsync({
            title: formData.title,
            contractNumber: formData.contractNumber,
            description: formData.description,
            supplierId: formData.supplierId,
            contractType: formData.contractType,
          });
          return true;

        case 'financial':
          await financialSchema.parseAsync({
            value: formData.value,
            currency: formData.currency,
            startDate: formData.startDate,
            endDate: formData.endDate,
          });
          return true;

        case 'terms':
          await termsSchema.parseAsync({
            paymentTerms: formData.paymentTerms,
          });
          return true;

        case 'performance':
        case 'compliance':
          // These steps are optional, always valid
          return true;

        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  };

  // Update step validation status
  const updateStepValidation = async () => {
    const newValidation: StepValidation = {
      basic: await validateStep('basic') ? 'valid' : 'invalid',
      financial: await validateStep('financial') ? 'valid' : 'invalid',
      terms: await validateStep('terms') ? 'valid' : 'invalid',
      performance: await validateStep('performance') ? 'valid' : 'invalid',
      compliance: await validateStep('compliance') ? 'valid' : 'invalid',
    };

    setStepValidation(newValidation);
  };

  // Handle step navigation with validation
  const navigateToStep = async (targetStep: string) => {
    const steps = ['basic', 'financial', 'terms', 'performance', 'compliance'];
    const currentIndex = steps.indexOf(activeTab);
    const targetIndex = steps.indexOf(targetStep);

    // If moving forward, validate current step
    if (targetIndex > currentIndex) {
      const isCurrentStepValid = await validateStep(activeTab);

      if (!isCurrentStepValid) {
        // Trigger form validation to show errors
        await form.trigger();

        // Mark this step as attempted
        setAttemptedSteps(prev => new Set([...prev, activeTab]));

        toast({
          title: 'Validation Error',
          description: `Please complete all required fields in the ${getStepLabel(activeTab)} step before proceeding.`,
          variant: 'destructive',
        });
        return;
      }
    }

    // Mark target step as attempted
    setAttemptedSteps(prev => new Set([...prev, targetStep]));
    setActiveTab(targetStep);
  };

  // Get step label for display
  const getStepLabel = (step: string): string => {
    const labels = {
      basic: 'Basic Information',
      financial: 'Financial Details',
      terms: 'Terms & Conditions',
      performance: 'Performance Metrics',
      compliance: 'Compliance & Legal'
    };
    return labels[step as keyof typeof labels] || step;
  };

  // Get step icon based on validation status
  const getStepIcon = (step: string) => {
    const isAttempted = attemptedSteps.has(step);
    const status = stepValidation[step as keyof StepValidation];

    if (!isAttempted) {
      return <Circle className="h-4 w-4 text-muted-foreground" />;
    }

    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'invalid':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Circle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  // Update validation status when form values change
  useEffect(() => {
    const subscription = form.watch(() => {
      updateStepValidation();
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Initial validation update
  useEffect(() => {
    updateStepValidation();
  }, []);

  const handleSubmit = async (data: ContractFormData) => {
    try {
      // Validate all steps before submission
      const allStepsValid = await Promise.all([
        validateStep('basic'),
        validateStep('financial'),
        validateStep('terms'),
        validateStep('performance'),
        validateStep('compliance')
      ]);

      if (!allStepsValid.every(Boolean)) {
        toast({
          title: 'Validation Error',
          description: 'Please complete all required fields in all steps before submitting.',
          variant: 'destructive',
        });
        return;
      }

      await onSubmit(data);
      toast({
        title: 'Success',
        description: 'Contract has been saved successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save contract. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {initialData ? 'Edit Contract' : 'Create New Contract'}
        </CardTitle>
        <CardDescription>
          {initialData ? 'Update contract details and terms' : 'Fill in the contract information across multiple steps'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={navigateToStep} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger
                  value="basic"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.basic === 'valid' && "text-green-600",
                    stepValidation.basic === 'invalid' && attemptedSteps.has('basic') && "text-red-600"
                  )}
                >
                  {getStepIcon('basic')}
                  Basic Info
                </TabsTrigger>
                <TabsTrigger
                  value="financial"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.financial === 'valid' && "text-green-600",
                    stepValidation.financial === 'invalid' && attemptedSteps.has('financial') && "text-red-600"
                  )}
                >
                  {getStepIcon('financial')}
                  Financial
                </TabsTrigger>
                <TabsTrigger
                  value="terms"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.terms === 'valid' && "text-green-600",
                    stepValidation.terms === 'invalid' && attemptedSteps.has('terms') && "text-red-600"
                  )}
                >
                  {getStepIcon('terms')}
                  Terms
                </TabsTrigger>
                <TabsTrigger
                  value="performance"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.performance === 'valid' && "text-green-600",
                    stepValidation.performance === 'invalid' && attemptedSteps.has('performance') && "text-red-600"
                  )}
                >
                  {getStepIcon('performance')}
                  Performance
                </TabsTrigger>
                <TabsTrigger
                  value="compliance"
                  className={cn(
                    "flex items-center gap-2",
                    stepValidation.compliance === 'valid' && "text-green-600",
                    stepValidation.compliance === 'invalid' && attemptedSteps.has('compliance') && "text-red-600"
                  )}
                >
                  {getStepIcon('compliance')}
                  Compliance
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.basic === 'invalid' && attemptedSteps.has('basic') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete all required fields in this step before proceeding.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contract Title *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter contract title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contractNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contract Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="CON-XXXX-YYYY" {...field} />
                        </FormControl>
                        <FormDescription>
                          Unique identifier for this contract
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="contractType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contract Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select contract type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {contractTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description *</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe the contract purpose and scope"
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supplierId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={suppliers.length === 0 ? "Loading suppliers..." : "Select supplier"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {suppliers.length === 0 ? (
                            <div className="px-2 py-1.5 text-sm text-muted-foreground">
                              No suppliers available
                            </div>
                          ) : (
                            suppliers.map((supplier) => (
                              <SelectItem key={supplier._id} value={supplier._id}>
                                {supplier.name}
                                {supplier.email && (
                                  <span className="text-xs text-muted-foreground ml-2">
                                    ({supplier.email})
                                  </span>
                                )}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="critical">Critical</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="riskLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Risk Level</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low Risk</SelectItem>
                            <SelectItem value="medium">Medium Risk</SelectItem>
                            <SelectItem value="high">High Risk</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="autoRenewal"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Auto Renewal
                          </FormLabel>
                          <FormDescription>
                            Enable automatic contract renewal
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                {form.watch("autoRenewal") && (
                  <FormField
                    control={form.control}
                    name="renewalTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Renewal Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Specify renewal terms and conditions"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </TabsContent>

              {/* Financial Details Tab */}
              <TabsContent value="financial" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.financial === 'invalid' && attemptedSteps.has('financial') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete all required financial fields before proceeding.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contract Value *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.value} value={currency.value}>
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="budgetCategory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={
                                budgetCategories.length === 0
                                  ? "Loading budget categories..."
                                  : "Select budget category"
                              } />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {budgetCategories.length === 0 ? (
                              <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                No budget categories available
                              </div>
                            ) : (
                              budgetCategories.map((category) => (
                                <SelectItem key={category._id} value={category._id}>
                                  {category.name}
                                  {category.description && (
                                    <span className="text-xs text-muted-foreground ml-2">
                                      - {category.description}
                                    </span>
                                  )}
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the budget category this contract will be charged to
                        </FormDescription>
                        <FormMessage />
                        {budgetCategories.length === 0 && (
                          <FormDescription className="text-amber-600">
                            No budget categories found. Please ensure budget categories are set up in the system.
                          </FormDescription>
                        )}
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0.00"
                            min="0"
                            max="100"
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Tax rate applicable to this contract
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Start Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>End Date *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date("1900-01-01")
                              }
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="critical">Critical</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="riskLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Risk Level</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low Risk</SelectItem>
                            <SelectItem value="medium">Medium Risk</SelectItem>
                            <SelectItem value="high">High Risk</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="autoRenewal"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Auto Renewal
                          </FormLabel>
                          <FormDescription>
                            Enable automatic contract renewal
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                {form.watch("autoRenewal") && (
                  <FormField
                    control={form.control}
                    name="renewalTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Renewal Terms</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Specify renewal terms and conditions"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </TabsContent>

              {/* Terms and Conditions Tab */}
              <TabsContent value="terms" className="space-y-4">
                {/* Step validation feedback */}
                {stepValidation.terms === 'invalid' && attemptedSteps.has('terms') && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please complete all required terms and conditions before proceeding.
                    </AlertDescription>
                  </Alert>
                )}

                <FormField
                  control={form.control}
                  name="paymentTerms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Terms *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Specify payment terms, schedules, and conditions"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="deliveryTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Delivery Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Delivery schedules, locations, and requirements"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="warrantyTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Warranty Terms</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Warranty period, coverage, and conditions"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="penaltyClause"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Penalty Clause</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Penalties for non-compliance or delays"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contract Terms */}
                <div className="space-y-3">
                  <FormLabel>Contract Terms & Conditions</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add a new term or condition"
                      value={newTerm}
                      onChange={(e) => setNewTerm(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTerm())}
                    />
                    <Button type="button" onClick={addTerm} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {termsFields.map((field, index) => (
                      <div key={field.id} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                        <span className="flex-1 text-sm">{form.watch(`terms.${index}`)}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTerm(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tags */}
                <div className="space-y-3">
                  <FormLabel>Tags</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add tags for categorization"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {tagsFields.map((field, index) => (
                      <Badge key={field.id} variant="secondary" className="flex items-center gap-1">
                        {form.watch(`tags.${index}`)}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => removeTag(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional notes or comments"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Performance Metrics Tab */}
              <TabsContent value="performance" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Performance Metrics</h3>
                      <p className="text-sm text-muted-foreground">
                        Define measurable performance indicators for this contract
                      </p>
                    </div>
                    <Button
                      type="button"
                      onClick={() => appendMetric({
                        metric: '',
                        target: '',
                        measurement: '',
                        frequency: 'monthly' as const
                      })}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Metric
                    </Button>
                  </div>

                  {metricsFields.map((field, index) => (
                    <Card key={field.id} className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">Metric {index + 1}</h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMetric(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`performanceMetrics.${index}.metric`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Metric Name</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., Response Time" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`performanceMetrics.${index}.target`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Target Value</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., < 24 hours" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`performanceMetrics.${index}.measurement`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Measurement Method</FormLabel>
                              <FormControl>
                                <Input placeholder="How will this be measured?" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`performanceMetrics.${index}.frequency`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Review Frequency</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {frequencies.map((freq) => (
                                    <SelectItem key={freq.value} value={freq.value}>
                                      {freq.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </Card>
                  ))}

                  {form.watch("performanceMetrics").length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                      <p>No performance metrics defined yet.</p>
                      <p className="text-sm">Click "Add Metric" to define performance indicators.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Compliance Tab */}
              <TabsContent value="compliance" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Compliance & Legal Requirements</h3>
                    <p className="text-sm text-muted-foreground">
                      Define compliance requirements and legal review settings
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="legalReviewRequired"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Legal Review Required
                          </FormLabel>
                          <FormDescription>
                            This contract requires legal department review before approval
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* Compliance Requirements */}
                  <div className="space-y-3">
                    <FormLabel>Compliance Requirements</FormLabel>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add compliance requirement"
                        value={newCompliance}
                        onChange={(e) => setNewCompliance(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCompliance())}
                      />
                      <Button type="button" onClick={addCompliance} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {complianceFields.map((field, index) => (
                        <div key={field.id} className="flex items-center gap-2 p-3 bg-muted rounded-md">
                          <Shield className="h-4 w-4 text-green-600" />
                          <span className="flex-1 text-sm">{form.watch(`complianceRequirements.${index}`)}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCompliance(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                    {complianceFields.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground text-sm">
                        No compliance requirements added yet
                      </div>
                    )}
                  </div>

                  {/* Notification Settings */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Notification Settings</h4>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="renewalReminder"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Renewal Reminders
                              </FormLabel>
                              <FormDescription>
                                Send renewal notifications
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="expiryReminder"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Expiry Reminders
                              </FormLabel>
                              <FormDescription>
                                Send expiry notifications
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="performanceReview"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Performance Reviews
                              </FormLabel>
                              <FormDescription>
                                Schedule performance reviews
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Reminder Days */}
                    <div className="space-y-3">
                      <FormLabel>Reminder Schedule (Days Before)</FormLabel>
                      <div className="flex flex-wrap gap-2">
                        {[7, 14, 30, 60, 90].map((days) => (
                          <Button
                            key={days}
                            type="button"
                            variant={form.watch("reminderDays").includes(days) ? "default" : "outline"}
                            size="sm"
                            onClick={() => {
                              const currentDays = form.getValues("reminderDays")
                              if (currentDays.includes(days)) {
                                form.setValue("reminderDays", currentDays.filter(d => d !== days))
                              } else {
                                form.setValue("reminderDays", [...currentDays, days].sort((a, b) => b - a))
                              }
                            }}
                          >
                            {days} days
                          </Button>
                        ))}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Selected: {form.watch("reminderDays").sort((a, b) => b - a).join(", ")} days before expiry
                      </p>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Step validation summary */}
            {Object.values(stepValidation).some(status => status === 'invalid') && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please complete all required fields in the highlighted steps before submitting.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const tabs = ['basic', 'financial', 'terms', 'performance', 'compliance'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex > 0) {
                    setActiveTab(tabs[currentIndex - 1]);
                  }
                }}
                disabled={activeTab === 'basic'}
              >
                Previous
              </Button>

              {activeTab === 'compliance' ? (
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Saving...' : initialData ? 'Update Contract' : 'Create Contract'}
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={async () => {
                    const tabs = ['basic', 'financial', 'terms', 'performance', 'compliance'];
                    const currentIndex = tabs.indexOf(activeTab);
                    if (currentIndex < tabs.length - 1) {
                      await navigateToStep(tabs[currentIndex + 1]);
                    }
                  }}
                >
                  Next
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
