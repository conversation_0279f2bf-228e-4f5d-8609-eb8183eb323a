#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript files
function findTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (file !== 'node_modules' && file !== '.next' && file !== '.git') {
        findTSFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix database imports in a file
function fixDatabaseImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file contains the incorrect import
    if (content.includes("from '@/lib/database'")) {
      console.log(`Fixing database import in: ${filePath}`);
      
      // Replace the incorrect import with the correct one
      const fixedContent = content.replace(
        /import\s*{\s*connectToDatabase\s*}\s*from\s*['"]@\/lib\/database['"]/g,
        "import { connectToDatabase } from '@/lib/backend/database'"
      );
      
      // Also fix any other database-related imports
      const finalContent = fixedContent.replace(
        /from\s*['"]@\/lib\/database['"]/g,
        "from '@/lib/backend/database'"
      );
      
      // Write the fixed content back to the file
      fs.writeFileSync(filePath, finalContent, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('🔧 Fixing database import paths...\n');
  
  const projectRoot = process.cwd();
  const tsFiles = findTSFiles(projectRoot);
  
  let fixedCount = 0;
  
  tsFiles.forEach(filePath => {
    if (fixDatabaseImports(filePath)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✅ Fixed ${fixedCount} files with incorrect database imports.`);
  
  if (fixedCount === 0) {
    console.log('🎉 All database imports are already correct!');
  } else {
    console.log('🎉 All database import paths have been standardized!');
  }
}

// Run the script
main();
