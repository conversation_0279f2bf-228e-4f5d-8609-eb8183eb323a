// lib/services/admin/database-remover-service.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import DeletedItems from '@/models/audit/DeletedItems';

// Import all models to ensure they're registered
import User from '@/models/User';
import { Employee } from '@/models/Employee';
import Department from '@/models/Department';
import Role from '@/models/Role';

// Import payroll models (using dynamic imports to avoid issues)
// These will be accessed via mongoose.models at runtime

export interface DatabaseModel {
  name: string;
  displayName: string;
  collection: string;
  model: mongoose.Model<any>;
  count: number;
  allowedRoles: UserRole[];
  dangerLevel: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  dependencies?: string[];
}

export interface DatabaseRemovalResult {
  success: boolean;
  deletedCount: number;
  auditRecords: string[];
  errors: string[];
  warnings: string[];
}

export interface BulkRemovalOptions {
  modelName: string;
  itemIds: string[];
  deletionReason: string;
  userInfo: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  confirmDeletion: boolean;
  deleteRelatedData?: boolean;
}

class DatabaseRemoverService {
  private static instance: DatabaseRemoverService;
  private modelRegistry: Map<string, DatabaseModel> = new Map();

  private constructor() {
    this.initializeModelRegistry();
  }

  public static getInstance(): DatabaseRemoverService {
    if (!DatabaseRemoverService.instance) {
      DatabaseRemoverService.instance = new DatabaseRemoverService();
    }
    return DatabaseRemoverService.instance;
  }

  private async initializeModelRegistry(): Promise<void> {
    await connectToDatabase();

    // Dynamically import models to ensure they're registered
    try {
      await import('@/models/payroll/PayrollRun');
      await import('@/models/payroll/PayrollRecord');
      await import('@/models/payroll/EmployeeSalary');
      await import('@/models/payroll/AuditLog');
      await import('@/models/accounting/Income');
      await import('@/models/accounting/Expense');
      await import('@/models/accounting/Budget');
    } catch (error) {
      logger.warn('Some models could not be imported', LogCategory.DATABASE, error);
    }

    // Core system models
    this.registerModel('User', {
      name: 'User',
      displayName: 'System Users',
      collection: 'users',
      model: User,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN],
      dangerLevel: 'critical',
      description: 'System user accounts and authentication data',
      dependencies: ['Employee', 'AuditLog', 'UserSession']
    });

    this.registerModel('Employee', {
      name: 'Employee',
      displayName: 'Employee Records',
      collection: 'employees',
      model: Employee,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Employee personal and employment information',
      dependencies: ['EmployeeSalary', 'PayrollRecord', 'Attendance']
    });

    this.registerModel('Department', {
      name: 'Department',
      displayName: 'Departments',
      collection: 'departments',
      model: Department,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Organizational departments and structure',
      dependencies: ['Employee', 'Budget']
    });

    this.registerModel('Role', {
      name: 'Role',
      displayName: 'System Roles',
      collection: 'roles',
      model: Role,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'medium',
      description: 'System roles and permissions configuration'
    });

    // Payroll models
    this.registerModel('PayrollRun', {
      name: 'PayrollRun',
      displayName: 'Payroll Runs',
      collection: 'payrollruns',
      model: mongoose.models.PayrollRun,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'critical',
      description: 'Payroll processing runs and calculations',
      dependencies: ['PayrollRecord', 'PaySlip']
    });

    this.registerModel('PayrollRecord', {
      name: 'PayrollRecord',
      displayName: 'Payroll Records',
      collection: 'payrollrecords',
      model: mongoose.models.PayrollRecord,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'critical',
      description: 'Individual employee payroll calculations',
      dependencies: ['PaySlip']
    });

    this.registerModel('EmployeeSalary', {
      name: 'EmployeeSalary',
      displayName: 'Employee Salaries',
      collection: 'employeesalaries',
      model: mongoose.models.EmployeeSalary,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Employee salary structures and configurations'
    });

    // Accounting models
    this.registerModel('Income', {
      name: 'Income',
      displayName: 'Income Records',
      collection: 'incomes',
      model: mongoose.models.Income,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Financial income transactions and records'
    });

    this.registerModel('Expense', {
      name: 'Expense',
      displayName: 'Expense Records',
      collection: 'expenses',
      model: mongoose.models.Expense,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Financial expense transactions and records'
    });

    this.registerModel('Budget', {
      name: 'Budget',
      displayName: 'Budget Records',
      collection: 'budgets',
      model: mongoose.models.Budget,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
      dangerLevel: 'high',
      description: 'Budget planning and allocation records'
    });

    // Audit and security models
    this.registerModel('AuditLog', {
      name: 'AuditLog',
      displayName: 'Audit Logs',
      collection: 'auditlogs',
      model: mongoose.models.AuditLog,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN],
      dangerLevel: 'critical',
      description: 'System audit trail and activity logs'
    });

    this.registerModel('DeletedItems', {
      name: 'DeletedItems',
      displayName: 'Deleted Items Archive',
      collection: 'deleted_items',
      model: DeletedItems,
      count: 0,
      allowedRoles: [UserRole.SUPER_ADMIN],
      dangerLevel: 'critical',
      description: 'Archive of deleted items for compliance and recovery'
    });

    // Update counts for all registered models
    await this.updateModelCounts();
  }

  private registerModel(name: string, config: Omit<DatabaseModel, 'count'>): void {
    this.modelRegistry.set(name, { ...config, count: 0 });
  }

  public async updateModelCounts(): Promise<void> {
    await connectToDatabase();
    
    for (const [name, config] of this.modelRegistry.entries()) {
      try {
        if (config.model) {
          const count = await config.model.countDocuments();
          this.modelRegistry.set(name, { ...config, count });
        }
      } catch (error) {
        logger.error(`Failed to count documents for model ${name}`, LogCategory.DATABASE, error);
        this.modelRegistry.set(name, { ...config, count: 0 });
      }
    }
  }

  public async getAllModels(): Promise<DatabaseModel[]> {
    await this.updateModelCounts();
    return Array.from(this.modelRegistry.values()).sort((a, b) => a.displayName.localeCompare(b.displayName));
  }

  public async getModelByName(name: string): Promise<DatabaseModel | null> {
    const model = this.modelRegistry.get(name);
    if (!model) return null;
    
    // Update count for this specific model
    try {
      if (model.model) {
        const count = await model.model.countDocuments();
        const updatedModel = { ...model, count };
        this.modelRegistry.set(name, updatedModel);
        return updatedModel;
      }
    } catch (error) {
      logger.error(`Failed to count documents for model ${name}`, LogCategory.DATABASE, error);
    }
    
    return model;
  }

  public async getModelData(modelName: string, page: number = 1, limit: number = 50): Promise<{
    items: any[];
    totalCount: number;
    totalPages: number;
  }> {
    await connectToDatabase();
    
    const model = this.modelRegistry.get(modelName);
    if (!model || !model.model) {
      throw new Error(`Model ${modelName} not found`);
    }

    const skip = (page - 1) * limit;
    const totalCount = await model.model.countDocuments();
    const totalPages = Math.ceil(totalCount / limit);

    const items = await model.model
      .find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    return {
      items: items.map(item => ({
        ...item,
        _id: item._id.toString()
      })),
      totalCount,
      totalPages
    };
  }

  public async bulkDeleteItems(options: BulkRemovalOptions): Promise<DatabaseRemovalResult> {
    const result: DatabaseRemovalResult = {
      success: false,
      deletedCount: 0,
      auditRecords: [],
      errors: [],
      warnings: []
    };

    try {
      await connectToDatabase();

      if (!options.confirmDeletion) {
        result.errors.push('Deletion confirmation required');
        return result;
      }

      if (options.deletionReason.length < 10) {
        result.errors.push('Deletion reason must be at least 10 characters');
        return result;
      }

      const model = this.modelRegistry.get(options.modelName);
      if (!model || !model.model) {
        result.errors.push(`Model ${options.modelName} not found`);
        return result;
      }

      // Check if user has permission for this model
      if (!model.allowedRoles.includes(options.userInfo.role as UserRole)) {
        result.errors.push(`Insufficient permissions to delete ${model.displayName}`);
        return result;
      }

      // Find items to delete
      const itemsToDelete = await model.model.find({
        _id: { $in: options.itemIds }
      });

      if (itemsToDelete.length === 0) {
        result.errors.push('No items found with provided IDs');
        return result;
      }

      // Create audit records before deletion
      const auditPromises = itemsToDelete.map(async (item) => {
        try {
          const auditRecord = await DeletedItems.createDeletedRecord(item, {
            deletedBy: options.userInfo.id,
            deletionReason: options.deletionReason,
            deletionType: 'bulk',
            userInfo: options.userInfo
          });
          return auditRecord._id.toString();
        } catch (error) {
          logger.error('Failed to create audit record', LogCategory.AUDIT, error);
          return null;
        }
      });

      const auditRecordIds = await Promise.all(auditPromises);
      result.auditRecords = auditRecordIds.filter(id => id !== null) as string[];

      // Perform deletion
      const deleteResult = await model.model.deleteMany({
        _id: { $in: options.itemIds }
      });

      result.deletedCount = deleteResult.deletedCount || 0;
      result.success = result.deletedCount > 0;

      if (result.deletedCount !== itemsToDelete.length) {
        result.warnings.push(`Expected to delete ${itemsToDelete.length} items, but only deleted ${result.deletedCount}`);
      }

      logger.info(`Bulk deletion completed for ${options.modelName}`, LogCategory.ADMIN, {
        modelName: options.modelName,
        deletedCount: result.deletedCount,
        userId: options.userInfo.id,
        reason: options.deletionReason
      });

    } catch (error) {
      logger.error('Bulk deletion failed', LogCategory.ADMIN, error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error occurred');
    }

    return result;
  }

  public async deleteAllModelData(modelName: string, userInfo: any, deletionReason: string): Promise<DatabaseRemovalResult> {
    const result: DatabaseRemovalResult = {
      success: false,
      deletedCount: 0,
      auditRecords: [],
      errors: [],
      warnings: []
    };

    try {
      const model = this.modelRegistry.get(modelName);
      if (!model || !model.model) {
        result.errors.push(`Model ${modelName} not found`);
        return result;
      }

      // Get all items for this model
      const allItems = await model.model.find().lean();
      const itemIds = allItems.map(item => item._id.toString());

      if (itemIds.length === 0) {
        result.warnings.push(`No data found in ${model.displayName}`);
        return result;
      }

      // Use bulk delete for all items
      return await this.bulkDeleteItems({
        modelName,
        itemIds,
        deletionReason,
        userInfo,
        confirmDeletion: true
      });

    } catch (error) {
      logger.error('Delete all model data failed', LogCategory.ADMIN, error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error occurred');
    }

    return result;
  }
}

export const databaseRemoverService = DatabaseRemoverService.getInstance();
