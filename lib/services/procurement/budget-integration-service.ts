// lib/services/procurement/budget-integration-service.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget, BudgetCategory, BudgetItem } from '@/models/accounting/Budget';
import { Transaction } from '@/models/accounting/Transaction';
import Expense from '@/models/accounting/Expense';
import Requisition from '@/models/procurement/Requisition';
import PurchaseOrder from '@/models/procurement/PurchaseOrder';
import Contract from '@/models/procurement/Contract';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';
import budgetService from '@/lib/services/accounting/budget-service';

/**
 * Interface for budget allocation
 */
interface BudgetAllocation {
  requisitionId: string;
  purchaseOrderId?: string;
  contractId?: string;
  budgetId: string;
  categoryId: string;
  itemId?: string;
  allocatedAmount: number;
  status: 'reserved' | 'committed' | 'spent' | 'released';
  allocatedAt: Date;
  releasedAt?: Date;
}

/**
 * Interface for budget check result
 */
interface BudgetCheckResult {
  available: boolean;
  availableAmount: number;
  requestedAmount: number;
  budgetId?: string;
  categoryId?: string;
  message: string;
}

/**
 * Service for integrating procurement with budget management
 */
export class ProcurementBudgetIntegrationService {
  private static instance: ProcurementBudgetIntegrationService;
  private allocations: Map<string, BudgetAllocation> = new Map();

  /**
   * Get singleton instance
   */
  static getInstance(): ProcurementBudgetIntegrationService {
    if (!this.instance) {
      this.instance = new ProcurementBudgetIntegrationService();
    }
    return this.instance;
  }

  /**
   * Check budget availability for a requisition
   * @param requisitionId - Requisition ID
   * @param amount - Amount to check
   * @param budgetCode - Budget code (optional)
   * @param categoryCode - Category code (optional)
   * @returns Budget check result
   */
  async checkBudgetAvailability(
    requisitionId: string,
    amount: number,
    budgetCode?: string,
    categoryCode?: string
  ): Promise<BudgetCheckResult> {
    try {
      await connectToDatabase();
      logger.info('Checking budget availability', LogCategory.PROCUREMENT, {
        requisitionId,
        amount,
        budgetCode,
        categoryCode
      });

      // Get requisition details
      const requisition = await Requisition.findById(requisitionId);
      if (!requisition) {
        throw new Error(`Requisition ${requisitionId} not found`);
      }

      // Find appropriate budget
      let budget;
      if (budgetCode) {
        budget = await Budget.findOne({ code: budgetCode, status: 'approved' });
      } else {
        // Find budget by fiscal year and department
        const currentYear = new Date().getFullYear();
        budget = await Budget.findOne({
          fiscalYear: currentYear,
          status: 'approved',
          startDate: { $lte: new Date() },
          endDate: { $gte: new Date() }
        });
      }

      if (!budget) {
        return {
          available: false,
          availableAmount: 0,
          requestedAmount: amount,
          message: 'No approved budget found for the specified criteria'
        };
      }

      // Find appropriate category
      let category;
      if (categoryCode) {
        category = budget.categories.find(cat => cat.code === categoryCode);
      } else {
        // Default to procurement category
        category = budget.categories.find(cat => 
          cat.name.toLowerCase().includes('procurement') || 
          cat.name.toLowerCase().includes('purchase')
        );
      }

      if (!category) {
        return {
          available: false,
          availableAmount: 0,
          requestedAmount: amount,
          budgetId: budget._id.toString(),
          message: 'No suitable budget category found'
        };
      }

      // Calculate available amount
      const allocatedAmount = await this.getTotalAllocatedAmount(budget._id.toString(), category._id.toString());
      const availableAmount = category.budgetedAmount - category.actualAmount - allocatedAmount;

      const result: BudgetCheckResult = {
        available: availableAmount >= amount,
        availableAmount,
        requestedAmount: amount,
        budgetId: budget._id.toString(),
        categoryId: category._id.toString(),
        message: availableAmount >= amount 
          ? 'Budget available for allocation'
          : `Insufficient budget. Available: ${availableAmount}, Requested: ${amount}`
      };

      logger.info('Budget availability check completed', LogCategory.PROCUREMENT, result);
      return result;

    } catch (error) {
      logger.error('Error checking budget availability', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Reserve budget for a requisition
   * @param requisitionId - Requisition ID
   * @param budgetId - Budget ID
   * @param categoryId - Category ID
   * @param amount - Amount to reserve
   * @param itemId - Budget item ID (optional)
   * @returns Allocation result
   */
  async reserveBudget(
    requisitionId: string,
    budgetId: string,
    categoryId: string,
    amount: number,
    itemId?: string
  ): Promise<BudgetAllocation> {
    try {
      await connectToDatabase();
      logger.info('Reserving budget', LogCategory.PROCUREMENT, {
        requisitionId,
        budgetId,
        categoryId,
        amount,
        itemId
      });

      // Check if already allocated
      const existingAllocation = this.allocations.get(requisitionId);
      if (existingAllocation) {
        throw new Error(`Budget already allocated for requisition ${requisitionId}`);
      }

      // Verify budget availability
      const checkResult = await this.checkBudgetAvailability(requisitionId, amount);
      if (!checkResult.available) {
        throw new Error(checkResult.message);
      }

      // Create allocation
      const allocation: BudgetAllocation = {
        requisitionId,
        budgetId,
        categoryId,
        itemId,
        allocatedAmount: amount,
        status: 'reserved',
        allocatedAt: new Date()
      };

      // Store allocation
      this.allocations.set(requisitionId, allocation);

      // Update requisition with budget allocation
      await Requisition.findByIdAndUpdate(requisitionId, {
        budgetAllocation: {
          budgetId,
          categoryId,
          itemId,
          allocatedAmount: amount,
          status: 'reserved'
        }
      });

      logger.info('Budget reserved successfully', LogCategory.PROCUREMENT, allocation);
      return allocation;

    } catch (error) {
      logger.error('Error reserving budget', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Commit budget when purchase order is created
   * @param purchaseOrderId - Purchase order ID
   * @param requisitionId - Requisition ID
   * @returns Updated allocation
   */
  async commitBudget(
    purchaseOrderId: string,
    requisitionId: string
  ): Promise<BudgetAllocation> {
    try {
      await connectToDatabase();
      logger.info('Committing budget', LogCategory.PROCUREMENT, {
        purchaseOrderId,
        requisitionId
      });

      const allocation = this.allocations.get(requisitionId);
      if (!allocation) {
        throw new Error(`No budget allocation found for requisition ${requisitionId}`);
      }

      if (allocation.status !== 'reserved') {
        throw new Error(`Budget allocation is not in reserved status: ${allocation.status}`);
      }

      // Update allocation
      allocation.purchaseOrderId = purchaseOrderId;
      allocation.status = 'committed';
      this.allocations.set(requisitionId, allocation);

      // Update purchase order with budget allocation
      await PurchaseOrder.findByIdAndUpdate(purchaseOrderId, {
        budgetAllocation: {
          budgetId: allocation.budgetId,
          categoryId: allocation.categoryId,
          itemId: allocation.itemId,
          allocatedAmount: allocation.allocatedAmount,
          status: 'committed'
        }
      });

      logger.info('Budget committed successfully', LogCategory.PROCUREMENT, allocation);
      return allocation;

    } catch (error) {
      logger.error('Error committing budget', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Record expenditure when goods are received
   * @param purchaseOrderId - Purchase order ID
   * @param actualAmount - Actual amount spent
   * @param description - Expenditure description
   * @returns Expenditure record
   */
  async recordExpenditure(
    purchaseOrderId: string,
    actualAmount: number,
    description: string
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Recording expenditure', LogCategory.PROCUREMENT, {
        purchaseOrderId,
        actualAmount,
        description
      });

      // Get purchase order
      const purchaseOrder = await PurchaseOrder.findById(purchaseOrderId);
      if (!purchaseOrder) {
        throw new Error(`Purchase order ${purchaseOrderId} not found`);
      }

      const allocation = this.allocations.get(purchaseOrder.requisitionId);
      if (!allocation) {
        throw new Error(`No budget allocation found for purchase order ${purchaseOrderId}`);
      }

      // Create expenditure record
      const expenditure = await Expense.create({
        description,
        amount: actualAmount,
        category: 'Procurement',
        subcategory: 'Purchase Orders',
        date: new Date(),
        budgetId: allocation.budgetId,
        budgetCategoryId: allocation.categoryId,
        budgetItemId: allocation.itemId,
        reference: {
          type: 'PurchaseOrder',
          id: purchaseOrderId
        },
        status: 'approved' // Auto-approve procurement expenditures
      });

      // Update budget category actual amount
      await Budget.updateOne(
        { 
          _id: allocation.budgetId,
          'categories._id': allocation.categoryId
        },
        {
          $inc: { 'categories.$.actualAmount': actualAmount }
        }
      );

      // Update allocation status
      allocation.status = 'spent';
      this.allocations.set(purchaseOrder.requisitionId, allocation);

      logger.info('Expenditure recorded successfully', LogCategory.PROCUREMENT, {
        expenditureId: expenditure._id,
        allocation
      });

      return expenditure;

    } catch (error) {
      logger.error('Error recording expenditure', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Release budget allocation (e.g., when requisition is cancelled)
   * @param requisitionId - Requisition ID
   * @returns Released allocation
   */
  async releaseBudget(requisitionId: string): Promise<BudgetAllocation | null> {
    try {
      await connectToDatabase();
      logger.info('Releasing budget', LogCategory.PROCUREMENT, { requisitionId });

      const allocation = this.allocations.get(requisitionId);
      if (!allocation) {
        logger.warn('No budget allocation found to release', LogCategory.PROCUREMENT, { requisitionId });
        return null;
      }

      if (allocation.status === 'spent') {
        throw new Error('Cannot release budget that has already been spent');
      }

      // Update allocation
      allocation.status = 'released';
      allocation.releasedAt = new Date();
      this.allocations.set(requisitionId, allocation);

      // Update requisition
      await Requisition.findByIdAndUpdate(requisitionId, {
        'budgetAllocation.status': 'released'
      });

      logger.info('Budget released successfully', LogCategory.PROCUREMENT, allocation);
      return allocation;

    } catch (error) {
      logger.error('Error releasing budget', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }

  /**
   * Get total allocated amount for a budget category
   * @param budgetId - Budget ID
   * @param categoryId - Category ID
   * @returns Total allocated amount
   */
  private async getTotalAllocatedAmount(budgetId: string, categoryId: string): Promise<number> {
    let total = 0;
    
    for (const allocation of this.allocations.values()) {
      if (allocation.budgetId === budgetId && 
          allocation.categoryId === categoryId && 
          ['reserved', 'committed'].includes(allocation.status)) {
        total += allocation.allocatedAmount;
      }
    }
    
    return total;
  }

  /**
   * Get budget utilization report
   * @param budgetId - Budget ID
   * @returns Utilization report
   */
  async getBudgetUtilization(budgetId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Getting budget utilization', LogCategory.PROCUREMENT, { budgetId });

      const budget = await Budget.findById(budgetId).populate('categories');
      if (!budget) {
        throw new Error(`Budget ${budgetId} not found`);
      }

      const utilization = {
        budgetId,
        totalBudgeted: budget.totalBudgeted,
        totalActual: budget.totalActualExpense,
        categories: budget.categories.map(category => {
          const allocatedAmount = Array.from(this.allocations.values())
            .filter(alloc => alloc.budgetId === budgetId && alloc.categoryId === category._id.toString())
            .reduce((sum, alloc) => sum + alloc.allocatedAmount, 0);

          return {
            categoryId: category._id,
            name: category.name,
            budgeted: category.budgetedAmount,
            actual: category.actualAmount,
            allocated: allocatedAmount,
            available: category.budgetedAmount - category.actualAmount - allocatedAmount,
            utilizationPercentage: ((category.actualAmount + allocatedAmount) / category.budgetedAmount) * 100
          };
        })
      };

      return utilization;

    } catch (error) {
      logger.error('Error getting budget utilization', LogCategory.PROCUREMENT, error);
      throw error;
    }
  }
}

// Export singleton instance
export const procurementBudgetIntegrationService = ProcurementBudgetIntegrationService.getInstance();
