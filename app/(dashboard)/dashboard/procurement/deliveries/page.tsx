// app/(dashboard)/dashboard/procurement/deliveries/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { DashboardHeader } from '@/components/dashboard-header';
import { DashboardShell } from '@/components/dashboard-shell';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui/data-table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { useProcurementStore } from '@/lib/stores/procurement-store';
import { DeliveryModal } from '@/components/procurement/modals/delivery-modal';
import { AuditDeletionDialog } from '@/components/ui/audit-deletion-dialog';
import { ErrorOverlay } from '@/components/errors/error-overlay';
import { ErrorState } from '@/components/ui/error-state';
import { AuditDeletionUI } from '@/lib/utils/audit-deletion-ui';

import { Plus, Truck, Package, Clock, CheckCircle, AlertTriangle, MapPin, Search, Filter, Settings, Eye, Edit, Trash2, Download, MoreHorizontal } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { Delivery } from '@/lib/stores/procurement-store';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function DeliveriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedDeliveries, setSelectedDeliveries] = useState<string[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState<Delivery | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(false);
  const [currentError, setCurrentError] = useState<any>(null);

  const {
    deliveries,
    isLoadingDeliveries,
    deliveriesError,
    deliveryFilters,
    deliveriesPagination,
    purchaseOrders,
    suppliers,
    fetchDeliveries,
    fetchPurchaseOrders,
    fetchSuppliers,
    createDelivery,
    updateDelivery,
    deleteDelivery,
    setDeliveryFilters,
    clearDeliveriesError
  } = useProcurementStore();

  // Column definitions for the deliveries table
  const deliveryColumns: ColumnDef<Delivery>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => {
            table.toggleAllPageRowsSelected(!!value);
            if (value) {
              setSelectedDeliveries(deliveries.map(d => d._id));
            } else {
              setSelectedDeliveries([]);
            }
          }}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => {
            row.toggleSelected(!!value);
            const deliveryId = row.original._id;
            if (value) {
              setSelectedDeliveries(prev => [...prev, deliveryId]);
            } else {
              setSelectedDeliveries(prev => prev.filter(id => id !== deliveryId));
            }
          }}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'deliveryNumber',
      header: 'Delivery #',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('deliveryNumber')}</div>
      ),
    },
    {
      accessorKey: 'supplier',
      header: 'Supplier',
      cell: ({ row }) => {
        const supplier = row.getValue('supplier') as any;
        return (
          <div>
            <div className="font-medium">{supplier.name}</div>
            <div className="text-sm text-muted-foreground">{supplier.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const variants = {
          scheduled: 'outline',
          in_transit: 'secondary',
          delivered: 'default',
          partially_delivered: 'secondary',
          delayed: 'destructive',
          cancelled: 'destructive',
          returned: 'destructive'
        } as const;

        return (
          <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
            {status.replace('_', ' ')}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'expectedDate',
      header: 'Expected Date',
      cell: ({ row }) => {
        const date = row.getValue('expectedDate') as Date;
        return format(new Date(date), 'MMM dd, yyyy');
      },
    },
    {
      accessorKey: 'deliveryAddress',
      header: 'Location',
      cell: ({ row }) => {
        const address = row.getValue('deliveryAddress') as any;
        return (
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{address.city}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'items',
      header: 'Items',
      cell: ({ row }) => {
        const items = row.getValue('items') as any[];
        return `${items.length} item(s)`;
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const delivery = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleViewDelivery(delivery)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditDelivery(delivery)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleDeleteSingle(delivery._id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Load data on component mount
  useEffect(() => {
    fetchDeliveries();
    fetchPurchaseOrders();
    fetchSuppliers();
  }, [fetchDeliveries, fetchPurchaseOrders, fetchSuppliers]);

  // Handle search and filters
  useEffect(() => {
    const filters = {
      search: searchTerm || undefined,
      status: (selectedStatus && selectedStatus !== 'all') ? selectedStatus : undefined,
    };

    setDeliveryFilters(filters);
    fetchDeliveries(1, 20, filters);
  }, [searchTerm, selectedStatus, setDeliveryFilters, fetchDeliveries]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearDeliveriesError();
    };
  }, [clearDeliveriesError]);

  // Handle error display
  useEffect(() => {
    if (deliveriesError) {
      setCurrentError(deliveriesError);
      setIsErrorOverlayOpen(true);
    }
  }, [deliveriesError]);

  // Event handlers
  const handleCreateDelivery = () => {
    setSelectedDelivery(null);
    setIsCreateModalOpen(true);
  };

  const handleViewDelivery = (delivery: Delivery) => {
    setSelectedDelivery(delivery);
    setIsViewModalOpen(true);
  };

  const handleEditDelivery = (delivery: Delivery) => {
    setSelectedDelivery(delivery);
    setIsEditModalOpen(true);
  };

  const handleDeleteSingle = (deliveryId: string) => {
    setSelectedDeliveries([deliveryId]);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteSelected = () => {
    if (selectedDeliveries.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select deliveries to delete.",
        variant: "destructive",
      });
      return;
    }
    setIsDeleteDialogOpen(true);
  };

  const handleRefresh = () => {
    fetchDeliveries();
    toast({
      title: 'Refreshed',
      description: 'Delivery data has been refreshed',
    });
  };

  const handleSubmitCreate = async (data: any) => {
    try {
      await createDelivery(data);
      setIsCreateModalOpen(false);
      toast({
        title: "Success",
        description: "Delivery created successfully.",
      });
    } catch (error) {
      console.error('Error creating delivery:', error);
      toast({
        title: "Error",
        description: "Failed to create delivery. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSubmitEdit = async (data: any) => {
    if (!selectedDelivery) return;

    try {
      await updateDelivery(selectedDelivery._id, data);
      setIsEditModalOpen(false);
      setSelectedDelivery(null);
      toast({
        title: "Success",
        description: "Delivery updated successfully.",
      });
    } catch (error) {
      console.error('Error updating delivery:', error);
      toast({
        title: "Error",
        description: "Failed to update delivery. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAuditDeletion = async (deletionReason: string) => {
    try {
      const result = await AuditDeletionUI.handleAuditDeletion(
        '/api/procurement/deliveries/audit-delete',
        selectedDeliveries,
        deletionReason,
        'deliveries'
      );

      if (result.success && result.result) {
        setIsDeleteDialogOpen(false);
        setSelectedDeliveries([]);
        fetchDeliveries(); // Refresh the list
        // The success toast is already shown by AuditDeletionUI.handleAuditDeletion
        console.log(`Successfully deleted ${result.result.deletedCount} deliveries`);
      }
    } catch (error) {
      console.error('Error deleting deliveries:', error);
      AuditDeletionUI.showErrorToast(
        error instanceof Error ? error.message : 'Failed to delete deliveries'
      );
    }
  };

  const handleErrorAction = async (action: string, data?: any) => {
    switch (action) {
      case 'retry':
        fetchDeliveries();
        break;
      case 'refresh':
        window.location.reload();
        break;
      case 'close':
        setIsErrorOverlayOpen(false);
        clearDeliveriesError();
        break;
      default:
        console.log('Unknown error action:', action);
    }
  };

  // Calculate stats
  const inTransitDeliveries = deliveries.filter(d => d.status === 'in_transit').length;
  const deliveredToday = deliveries.filter(d => {
    const today = new Date();
    const deliveryDate = d.actualDate ? new Date(d.actualDate) : null;
    return deliveryDate &&
           deliveryDate.toDateString() === today.toDateString() &&
           d.status === 'delivered';
  }).length;
  const pendingReceipt = deliveries.filter(d => d.status === 'delivered' && (!d.receipt || !d.receipt.received)).length;
  const qualityIssues = deliveries.filter(d =>
    d.inspection && !d.inspection.passed
  ).length;

  // Get unique locations
  const uniqueLocations = [...new Set(deliveries.map(d => d.deliveryAddress?.city || 'Unknown').filter(Boolean))];

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Delivery Tracking"
        text="Track deliveries, manage receipts, and conduct quality inspections."
      >
        <div className="flex items-center gap-2">
          {selectedDeliveries.length > 0 && (
            <Button variant="destructive" onClick={handleDeleteSelected}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Selected ({selectedDeliveries.length})
            </Button>
          )}
          <Button variant="outline" onClick={handleRefresh}>
            <Settings className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={handleCreateDelivery}>
            <Plus className="mr-2 h-4 w-4" />
            Schedule Delivery
          </Button>
        </div>
      </DashboardHeader>

      {/* Error Display */}
      {deliveriesError && !isErrorOverlayOpen && (
        <ErrorState
          message="Failed to load deliveries"
          error={new Error(deliveriesError)}
          onRetry={handleRefresh}
        />
      )}

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inTransitDeliveries}</div>
            <p className="text-xs text-muted-foreground">
              Currently shipping
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered Today</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deliveredToday}</div>
            <p className="text-xs text-muted-foreground">
              Completed deliveries
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Receipt</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingReceipt}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting confirmation
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qualityIssues}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="list" className="space-y-6">
        <TabsList>
          <TabsTrigger value="list">Delivery List</TabsTrigger>
          <TabsTrigger value="tracking">Live Tracking</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search deliveries..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="partially_delivered">Partially Delivered</SelectItem>
                    <SelectItem value="delayed">Delayed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="returned">Returned</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {uniqueLocations.map(location => (
                      <SelectItem key={location} value={location}>{location}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Deliveries Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Deliveries ({deliveries.length})
              </CardTitle>
              <CardDescription>
                Track and manage all delivery operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={deliveryColumns}
                data={deliveries}
                isLoading={isLoadingDeliveries}
                searchKey="deliveryNumber"
                searchPlaceholder="Search deliveries..."
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Delivery Locations
                </CardTitle>
                <CardDescription>
                  Active delivery destinations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {uniqueLocations.map(location => {
                    const locationDeliveries = deliveries.filter(d => d.location === location);
                    const activeDeliveries = locationDeliveries.filter(d =>
                      ['scheduled', 'in_transit'].includes(d.status)
                    ).length;

                    return (
                      <div key={location} className="flex items-start gap-3">
                        <MapPin className="h-4 w-4 text-blue-500 mt-0.5" />
                        <div>
                          <p className="font-medium">{location}</p>
                          <p className="text-sm text-muted-foreground">
                            {activeDeliveries} active deliveries
                          </p>
                        </div>
                      </div>
                    );
                  })}

                  {uniqueLocations.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No delivery locations found
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest delivery updates and status changes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {deliveries.slice(0, 5).map(delivery => (
                    <div key={delivery._id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          delivery.status === 'delivered' ? 'bg-green-500' :
                          delivery.status === 'in_transit' ? 'bg-blue-500' :
                          delivery.status === 'delayed' ? 'bg-red-500' :
                          'bg-orange-500'
                        }`}></div>
                        <div>
                          <p className="font-medium">{delivery.deliveryNumber}</p>
                          <p className="text-sm text-muted-foreground">{delivery.supplier.name}</p>
                        </div>
                      </div>
                      <Badge variant={
                        delivery.status === 'delivered' ? 'default' :
                        delivery.status === 'in_transit' ? 'secondary' :
                        delivery.status === 'delayed' ? 'destructive' :
                        'outline'
                      }>
                        {delivery.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  ))}

                  {deliveries.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No recent activity
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Delivery Status Distribution</CardTitle>
                <CardDescription>
                  Breakdown of deliveries by status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['scheduled', 'in_transit', 'delivered', 'delayed', 'cancelled'].map(status => {
                    const count = deliveries.filter(d => d.status === status).length;
                    const percentage = deliveries.length > 0 ? (count / deliveries.length * 100).toFixed(1) : '0';
                    return (
                      <div key={status} className="flex items-center justify-between">
                        <span className="capitalize">{status.replace('_', ' ')}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{count}</span>
                          <span className="text-sm font-medium">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Location Performance</CardTitle>
                <CardDescription>
                  Delivery performance by location
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {uniqueLocations.map(location => {
                    const locationDeliveries = deliveries.filter(d => d.location === location);
                    const deliveredCount = locationDeliveries.filter(d => d.status === 'delivered').length;
                    const successRate = locationDeliveries.length > 0 ?
                      (deliveredCount / locationDeliveries.length * 100).toFixed(1) : '0';

                    return (
                      <div key={location} className="flex items-center justify-between">
                        <span>{location}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{deliveredCount}/{locationDeliveries.length}</span>
                          <span className="text-sm font-medium">{successRate}%</span>
                        </div>
                      </div>
                    );
                  })}

                  {uniqueLocations.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No location data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <DeliveryModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        mode="create"
        onSubmit={handleSubmitCreate}
        purchaseOrders={purchaseOrders}
        suppliers={suppliers}
        isLoading={isLoadingDeliveries}
      />

      <DeliveryModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        mode="edit"
        delivery={selectedDelivery || undefined}
        onSubmit={handleSubmitEdit}
        purchaseOrders={purchaseOrders}
        suppliers={suppliers}
        isLoading={isLoadingDeliveries}
      />

      <DeliveryModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        mode="view"
        delivery={selectedDelivery || undefined}
        purchaseOrders={purchaseOrders}
        suppliers={suppliers}
      />

      {/* Audit Deletion Dialog */}
      <AuditDeletionDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleAuditDeletion}
        selectedCount={selectedDeliveries.length}
        itemType="deliveries"
      />

      {/* Error Overlay */}
      <ErrorOverlay
        error={currentError}
        isOpen={isErrorOverlayOpen}
        onClose={() => setIsErrorOverlayOpen(false)}
        onAction={handleErrorAction}
      />
    </DashboardShell>
  );
}
