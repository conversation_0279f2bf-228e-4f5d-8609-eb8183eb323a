// app/(dashboard)/dashboard/procurement/requisitions/create/page.tsx
import { Metadata } from "next"
import { redirect } from "next/navigation"
import { getServerSession } from "@/lib/backend/auth/session"
import { hasRequiredPermissions } from "@/lib/backend/utils/permissions"
import { UserRole } from "@/types/user-roles"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { CreateRequisitionPage } from "@/components/procurement/create-requisition-page"

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Create Purchase Requisition",
  description: "Create a new purchase requisition for approval",
}

export default async function CreateRequisitionPageWrapper() {
  // Get current user
  const user = await getCurrentUser();

  // Redirect if not authenticated
  if (!user) {
    redirect('/login');
  }

  // Check permissions - Allow all authenticated users to create requisitions
  const hasPermission = hasRequiredPermissions(user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.PROCUREMENT_MANAGER,
    UserRole.PROCUREMENT_OFFICER,
    UserRole.DEPARTMENT_HEAD,
    UserRole.TEAM_LEADER,
    UserRole.EMPLOYEE,
    UserRole.CONTRACTOR,
    UserRole.INTERN
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Create Purchase Requisition"
        text="Create a new purchase requisition for approval and procurement"
      />
      <div className="flex-1 space-y-6">
        <CreateRequisitionPage userId={user.id} />
      </div>
    </DashboardShell>
  );
}
