'use client';

import { useState, useEffect } from 'react';
import { DashboardHeader } from '@/components/dashboard-header';
import { DashboardShell } from '@/components/dashboard-shell';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useProcurementStore } from '@/lib/stores/procurement-store';
import { Plus, FileText, Calendar, DollarSign, Users, AlertTriangle, Search, Filter, Settings, Eye, Edit, Trash2 } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { Contract } from '@/lib/stores/procurement-store';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import { ContractForm } from '@/components/procurement/forms/contract-form';


// Column definitions for the contracts table
const contractColumns: ColumnDef<Contract>[] = [
  {
    accessorKey: 'contractNumber',
    header: 'Contract #',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('contractNumber')}</div>
    ),
  },
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => (
      <div>
        <div className="font-medium">{row.getValue('title')}</div>
        <div className="text-sm text-muted-foreground">{row.original.supplier.name}</div>
      </div>
    ),
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const type = row.getValue('type') as string;
      return <Badge variant="outline">{type}</Badge>;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const variants = {
        draft: 'outline',
        active: 'default',
        suspended: 'secondary',
        terminated: 'destructive',
        expired: 'destructive',
        completed: 'default'
      } as const;

      return (
        <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'value',
    header: 'Value',
    cell: ({ row }) => {
      const value = row.getValue('value') as number;
      const currency = row.original.currency;
      return `${currency} ${value.toLocaleString()}`;
    },
  },
  {
    accessorKey: 'endDate',
    header: 'End Date',
    cell: ({ row }) => {
      const date = row.getValue('endDate') as Date;
      return format(new Date(date), 'MMM dd, yyyy');
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

export default function ContractsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    contracts,
    isLoadingContracts,
    contractsError,
    contractFilters,
    contractsPagination,
    fetchContracts,
    setContractFilters,
    clearContractsError,
    createContract,
    suppliers,
    isLoadingSuppliers,
    fetchSuppliers,
    budgetCategories,
    isLoadingBudgetCategories,
    fetchBudgetCategories,
    costCenters,
    isLoadingCostCenters,
    fetchCostCenters
  } = useProcurementStore();

  // Load contracts on component mount
  useEffect(() => {
    fetchContracts();
  }, [fetchContracts]);

  // Load suppliers for the contract form dropdown
  useEffect(() => {
    console.log('Checking suppliers:', { suppliersLength: suppliers.length, isLoadingSuppliers });
    if (suppliers.length === 0 && !isLoadingSuppliers) {
      console.log('Fetching suppliers...');
      fetchSuppliers(1, 100); // Fetch first 100 suppliers for dropdown
    }
  }, [fetchSuppliers, suppliers.length, isLoadingSuppliers]);

  // Load budget categories for the contract form dropdown
  useEffect(() => {
    console.log('Checking budget categories:', { budgetCategoriesLength: budgetCategories.length, isLoadingBudgetCategories });
    if (budgetCategories.length === 0 && !isLoadingBudgetCategories) {
      console.log('Fetching budget categories...');
      fetchBudgetCategories();
    }
  }, [fetchBudgetCategories, budgetCategories.length, isLoadingBudgetCategories]);

  // Load cost centers for the contract form dropdown
  useEffect(() => {
    console.log('Checking cost centers:', { costCentersLength: costCenters.length, isLoadingCostCenters });
    if (costCenters.length === 0 && !isLoadingCostCenters) {
      console.log('Fetching cost centers...');
      fetchCostCenters();
    }
  }, [fetchCostCenters, costCenters.length, isLoadingCostCenters]);

  // Handle search and filters
  useEffect(() => {
    const filters = {
      search: searchTerm || undefined,
      type: (selectedType && selectedType !== 'all') ? selectedType : undefined,
      status: (selectedStatus && selectedStatus !== 'all') ? selectedStatus : undefined,
    };

    setContractFilters(filters);
    fetchContracts(1, 20, filters);
  }, [searchTerm, selectedType, selectedStatus, setContractFilters, fetchContracts]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearContractsError();
    };
  }, [clearContractsError]);

  const handleOpenCreateDialog = () => {
    setIsCreateDialogOpen(true);
  };

  const handleRefresh = () => {
    fetchContracts();
    fetchSuppliers(1, 100); // Also refresh suppliers
    fetchBudgetCategories(); // Also refresh budget categories
    fetchCostCenters(); // Also refresh cost centers
    toast({
      title: 'Refreshed',
      description: 'All contract form data has been refreshed',
    });
  };

  const handleCreateContract = async (data: any) => {
    setIsSubmitting(true);
    try {
      await createContract(data);
      setIsCreateDialogOpen(false);
      fetchContracts();
      toast({
        title: 'Success',
        description: 'Contract has been created successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create contract. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate stats
  const activeContracts = contracts.filter(c => c.status === 'active').length;
  const totalValue = contracts.reduce((sum, c) => sum + c.value, 0);
  const expiringContracts = contracts.filter(c => {
    const endDate = new Date(c.endDate);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return endDate <= thirtyDaysFromNow && c.status === 'active';
  }).length;
  const uniqueSuppliers = new Set(contracts.map(c => c.supplier._id)).size;

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Contract Management"
        text="Manage procurement contracts, renewals, and compliance."
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <Settings className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={handleOpenCreateDialog}>
            <Plus className="mr-2 h-4 w-4" />
            New Contract
          </Button>
        </div>
      </DashboardHeader>

      {/* Error Display */}
      {contractsError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-4 w-4" />
              <span>{contractsError}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Contracts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeContracts}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalValue / 1000000).toFixed(1)}M</div>
            <p className="text-xs text-muted-foreground">
              Combined contract value
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{expiringContracts}</div>
            <p className="text-xs text-muted-foreground">
              Next 30 days
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Suppliers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              Active suppliers
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="list" className="space-y-6">
        <TabsList>
          <TabsTrigger value="list">Contract List</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search contracts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Contract Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="service">Service</SelectItem>
                    <SelectItem value="goods">Goods</SelectItem>
                    <SelectItem value="works">Works</SelectItem>
                    <SelectItem value="consultancy">Consultancy</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Contracts Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Contracts ({contracts.length})
              </CardTitle>
              <CardDescription>
                Manage all procurement contracts and their lifecycle
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                columns={contractColumns}
                data={contracts}
                isLoading={isLoadingContracts}
                searchKey="title"
                searchPlaceholder="Search contracts..."
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Contract Types Distribution</CardTitle>
                <CardDescription>
                  Breakdown of contracts by type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['service', 'goods', 'works', 'consultancy'].map(type => {
                    const count = contracts.filter(c => c.type === type).length;
                    const percentage = contracts.length > 0 ? (count / contracts.length * 100).toFixed(1) : '0';
                    return (
                      <div key={type} className="flex items-center justify-between">
                        <span className="capitalize">{type}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{count}</span>
                          <span className="text-sm font-medium">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contract Status Overview</CardTitle>
                <CardDescription>
                  Current status of all contracts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['active', 'draft', 'suspended', 'expired', 'completed'].map(status => {
                    const count = contracts.filter(c => c.status === status).length;
                    const percentage = contracts.length > 0 ? (count / contracts.length * 100).toFixed(1) : '0';
                    return (
                      <div key={status} className="flex items-center justify-between">
                        <span className="capitalize">{status}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{count}</span>
                          <span className="text-sm font-medium">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Contract Alerts
              </CardTitle>
              <CardDescription>
                Important notifications and reminders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {expiringContracts > 0 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg bg-orange-50 border-orange-200">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Contracts Expiring Soon</p>
                      <p className="text-sm text-muted-foreground">
                        {expiringContracts} contract(s) expire within the next 30 days
                      </p>
                    </div>
                  </div>
                )}

                {contracts.filter(c => c.status === 'draft').length > 0 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg bg-blue-50 border-blue-200">
                    <AlertTriangle className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Draft Contracts Pending</p>
                      <p className="text-sm text-muted-foreground">
                        {contracts.filter(c => c.status === 'draft').length} contract(s) in draft status need attention
                      </p>
                    </div>
                  </div>
                )}

                {contracts.filter(c => c.compliance.score < 70).length > 0 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg bg-red-50 border-red-200">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Low Compliance Scores</p>
                      <p className="text-sm text-muted-foreground">
                        {contracts.filter(c => c.compliance.score < 70).length} contract(s) have compliance scores below 70%
                      </p>
                    </div>
                  </div>
                )}

                {expiringContracts === 0 && contracts.filter(c => c.status === 'draft').length === 0 && contracts.filter(c => c.compliance.score < 70).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No alerts at this time
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Contract Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[95vw] max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Contract</DialogTitle>
            <DialogDescription>
              Add a new procurement contract to the system
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <ContractForm
              onSubmit={handleCreateContract}
              isLoading={isSubmitting}
              suppliers={suppliers.map(supplier => ({
                _id: supplier._id,
                name: supplier.name,
                email: supplier.email || supplier.contactPerson || ''
              }))}
              budgetCategories={budgetCategories.map(category => ({
                _id: category._id,
                name: category.name,
                description: category.description
              }))}
              costCenters={costCenters.map(center => ({
                _id: center._id,
                name: center.name
              }))}
            />
          </div>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  );
}
