import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { procurementBudgetIntegrationService } from '@/lib/services/procurement/budget-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for budget availability check
const budgetCheckSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  budgetCode: z.string().optional(),
  categoryCode: z.string().optional(),
  description: z.string().optional()
});

/**
 * POST /api/procurement/budget/check-availability
 * Check budget availability for a procurement request
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Validate request body
    const validationResult = budgetCheckSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        message: validationResult.error.errors[0].message,
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { amount, budgetCode, categoryCode, description } = validationResult.data;

    logger.info('Budget availability check requested', LogCategory.PROCUREMENT, {
      amount,
      budgetCode,
      categoryCode,
      description,
      userId: user.id
    });

    // Check budget availability
    const budgetCheck = await procurementBudgetIntegrationService.checkBudgetAvailability(
      'check-' + Date.now(),
      amount,
      budgetCode,
      categoryCode
    );

    logger.info('Budget availability check completed', LogCategory.PROCUREMENT, {
      amount,
      available: budgetCheck.available,
      availableAmount: budgetCheck.availableAmount,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      budgetCheck: {
        available: budgetCheck.available,
        availableAmount: budgetCheck.availableAmount,
        requestedAmount: budgetCheck.requestedAmount,
        budgetId: budgetCheck.budgetId,
        categoryId: budgetCheck.categoryId,
        message: budgetCheck.message
      }
    });

  } catch (error) {
    logger.error('Error checking budget availability', LogCategory.PROCUREMENT, error);
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Unable to check budget availability. Please try again or contact support.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET /api/procurement/budget/check-availability
 * Get budget availability information (for quick checks)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const amount = parseFloat(searchParams.get('amount') || '0');
    const budgetCode = searchParams.get('budgetCode') || undefined;
    const categoryCode = searchParams.get('categoryCode') || undefined;

    if (!amount || amount <= 0) {
      return NextResponse.json({
        error: 'Invalid amount',
        message: 'Amount must be greater than 0'
      }, { status: 400 });
    }

    logger.info('Budget availability check requested via GET', LogCategory.PROCUREMENT, {
      amount,
      budgetCode,
      categoryCode,
      userId: user.id
    });

    // Check budget availability
    const budgetCheck = await procurementBudgetIntegrationService.checkBudgetAvailability(
      'check-' + Date.now(),
      amount,
      budgetCode,
      categoryCode
    );

    return NextResponse.json({
      success: true,
      budgetCheck: {
        available: budgetCheck.available,
        availableAmount: budgetCheck.availableAmount,
        requestedAmount: budgetCheck.requestedAmount,
        budgetId: budgetCheck.budgetId,
        categoryId: budgetCheck.categoryId,
        message: budgetCheck.message
      }
    });

  } catch (error) {
    logger.error('Error checking budget availability via GET', LogCategory.PROCUREMENT, error);
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Unable to check budget availability. Please try again or contact support.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
