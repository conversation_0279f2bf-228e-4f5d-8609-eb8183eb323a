// app/api/procurement/purchase-orders/route.ts
import { NextRequest, NextResponse } from 'next/server';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  orderDate?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}


import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PurchaseOrderService, PurchaseOrderImportExportService, PurchaseOrderReportingService } from '@/lib/backend/services/procurement/PurchaseOrderService';
import { procurementBudgetIntegrationService } from '@/lib/services/procurement/budget-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Initialize services
const purchaseOrderService = new PurchaseOrderService();
const importExportService = new PurchaseOrderImportExportService();
const reportingService = new PurchaseOrderReportingService();

/**
 * GET handler for purchase orders
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const supplierId = searchParams.get('supplierId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get purchase order by ID
      const purchaseOrder = await purchaseOrderService.getOrderDetails(id);

      if (!purchaseOrder) {
        return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
      }

      // Generate report if requested
      if (report === 'details') {
        const pdfBuffer = await reportingService.generateOrderDetailsReport(id);

        // Convert Buffer to Uint8Array which is acceptable for NextResponse
        return new NextResponse(new Uint8Array(pdfBuffer), {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="purchase-order-${id}.pdf"`
          }
        });
      }

      return NextResponse.json(purchaseOrder);
    } else if (format) {
      // Export purchase orders
      const filter: MongoFilter = {};

      // Add filters
      if (supplierId) {
        filter.supplierId = supplierId;
      }

      if (status) {
        filter.status = status;
      }

      if (startDate || endDate) {
        filter.orderDate = {};

        if (startDate) {
          filter.orderDate.$gte = new Date(startDate);
        }

        if (endDate) {
          filter.orderDate.$lte = new Date(endDate);
        }
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'purchase-orders.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'purchase-orders.csv';
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      return new NextResponse(new Uint8Array(buffer), {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report === 'summary') {
      // Generate summary report
      interface ReportOptions {
        startDate?: Date;
        endDate?: Date;
        supplierId?: string;
        [key: string]: any;
      }

      const options: ReportOptions = {};

      if (startDate) {
        options.startDate = new Date(startDate);
      }

      if (endDate) {
        options.endDate = new Date(endDate);
      }

      if (supplierId) {
        options.supplierId = supplierId;
      }

      const summaryReport = await reportingService.generateSummaryReport(options);

      return NextResponse.json(summaryReport);
    } else {
      // Get purchase orders with pagination
      let result;

      if (supplierId) {
        // Get by supplier
        interface SupplierOptions {
          page: number;
          limit: number;
          status?: string;
          startDate?: Date;
          endDate?: Date;
          [key: string]: any;
        }

        const options: SupplierOptions = {
          page,
          limit,
        };

        if (status) {
          options.status = status;
        }

        if (startDate) {
          options.startDate = new Date(startDate);
        }

        if (endDate) {
          options.endDate = new Date(endDate);
        }

        // Cast to any to bypass TypeScript's type checking
        result = await purchaseOrderService.getBySupplier(supplierId, options as any);
      } else if (startDate && endDate) {
        // Get by date range
        interface DateRangeOptions {
          page: number;
          limit: number;
          status?: string;
          supplierId?: string;
          [key: string]: any;
        }

        const options: DateRangeOptions = {
          page,
          limit,
        };

        if (status) {
          options.status = status;
        }

        if (supplierId) {
          options.supplierId = supplierId;
        }

        // Cast to any to bypass TypeScript's type checking
        result = await purchaseOrderService.getByDateRange(
          new Date(startDate),
          new Date(endDate),
          options as any
        );
      } else {
        // Get all with pagination
        const filter: MongoFilter = {};

        if (status) {
          filter.status = status;
        }

        result = await purchaseOrderService.paginate(filter, page, limit, { createdAt: -1 }, ['supplierId', 'requisitionId', 'createdBy', 'approvedBy']);
      }

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in purchase orders GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for purchase orders
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.FINANCE_DIRECTOR,
        UserRole.PROCUREMENT_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // Create purchase order
    const purchaseOrder = await purchaseOrderService.createPurchaseOrder(body);

    // If created from requisition, commit the reserved budget
    if (body.requisitionId) {
      try {
        logger.info('Committing budget for purchase order', LogCategory.PROCUREMENT, {
          purchaseOrderId: purchaseOrder._id,
          requisitionId: body.requisitionId,
          userId: user.id
        });

        await procurementBudgetIntegrationService.commitBudget(
          purchaseOrder._id.toString(),
          body.requisitionId
        );

        logger.info('Budget committed successfully for purchase order', LogCategory.PROCUREMENT, {
          purchaseOrderId: purchaseOrder._id,
          requisitionId: body.requisitionId,
          userId: user.id
        });
      } catch (error) {
        logger.error('Failed to commit budget for purchase order', LogCategory.PROCUREMENT, {
          purchaseOrderId: purchaseOrder._id,
          requisitionId: body.requisitionId,
          error: error instanceof Error ? error.message : 'Unknown error',
          userId: user.id
        });

        // Consider whether to rollback PO creation or continue with warning
        // For now, we'll continue but log the error and notify the user
        logger.warn('Purchase order created but budget commitment failed', LogCategory.PROCUREMENT, {
          purchaseOrderId: purchaseOrder._id,
          requisitionId: body.requisitionId,
          userId: user.id
        });

        // Add a warning to the response
        return NextResponse.json({
          ...purchaseOrder.toObject(),
          warning: 'Purchase order created successfully, but budget commitment failed. Please contact finance team.',
          budgetCommitmentError: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 201 });
      }
    }

    logger.info('Purchase order created successfully', LogCategory.PROCUREMENT, {
      purchaseOrderId: purchaseOrder._id,
      requisitionId: body.requisitionId,
      totalAmount: purchaseOrder.totalAmount,
      budgetCommitted: !!body.requisitionId,
      userId: user.id
    });

    return NextResponse.json(purchaseOrder, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in purchase orders POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for purchase orders
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if status update
    if (body.id && body.status) {
      // Get purchase order details before status update
      const existingPurchaseOrder = await purchaseOrderService.findById(body.id);

      if (!existingPurchaseOrder) {
        return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
      }

      // Budget operations for status changes
      if (body.status === 'cancelled') {
        // Release committed budget when PO is cancelled
        if (existingPurchaseOrder.requisitionId) {
          try {
            logger.info('Releasing budget for cancelled purchase order', LogCategory.PROCUREMENT, {
              purchaseOrderId: body.id,
              requisitionId: existingPurchaseOrder.requisitionId,
              userId: user.id
            });

            await procurementBudgetIntegrationService.releaseBudget(existingPurchaseOrder.requisitionId);

            logger.info('Budget released successfully for cancelled purchase order', LogCategory.PROCUREMENT, {
              purchaseOrderId: body.id,
              requisitionId: existingPurchaseOrder.requisitionId,
              userId: user.id
            });
          } catch (error) {
            logger.warn('Failed to release budget for cancelled purchase order', LogCategory.PROCUREMENT, {
              purchaseOrderId: body.id,
              requisitionId: existingPurchaseOrder.requisitionId,
              error: error instanceof Error ? error.message : 'Unknown error',
              userId: user.id
            });
            // Continue with cancellation even if budget release fails
          }
        }
      }

      // Update status
      const purchaseOrder = await purchaseOrderService.updateStatus(
        body.id,
        body.status,
        user.id
      );

      if (!purchaseOrder) {
        return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
      }

      logger.info('Purchase order status updated successfully', LogCategory.PROCUREMENT, {
        purchaseOrderId: body.id,
        oldStatus: existingPurchaseOrder.status,
        newStatus: body.status,
        userId: user.id
      });

      return NextResponse.json(purchaseOrder);
    } else if (body.id) {
      // Update purchase order
      const purchaseOrder = await purchaseOrderService.updateById(body.id, body);

      if (!purchaseOrder) {
        return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
      }

      return NextResponse.json(purchaseOrder);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in purchase orders PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

