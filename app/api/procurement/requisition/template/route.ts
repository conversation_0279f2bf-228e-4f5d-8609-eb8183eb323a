import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectToDatabase } from '@/lib/backend/database';
import Department from '@/models/Department';
import { Budget } from '@/models/accounting/Budget';
import { User } from '@/models/User';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function GET(request: NextRequest) {
  try {
    logger.info('Generating requisition template with real data', LogCategory.PROCUREMENT);

    // Connect to database
    await connectToDatabase();

    // Fetch real data from database
    const [departments, budgets, users] = await Promise.all([
      Department.find({ status: 'active' }).select('name').limit(10),
      Budget.find({ status: 'approved' }).select('name fiscalYear').limit(5),
      User.find({ status: 'active' }).select('firstName lastName email').limit(10)
    ]);

    logger.info('Fetched template data', LogCategory.PROCUREMENT, {
      departmentsCount: departments.length,
      budgetsCount: budgets.length,
      usersCount: users.length
    });

    // Create sample data with real departments and users
    const sampleData = [];

    // Generate multiple sample rows using real data
    const sampleItems = [
      {
        title: 'Office Equipment Purchase',
        description: 'Purchase of laptops and monitors for new employees',
        justification: 'New hires require equipment to perform their duties effectively',
        itemName: 'Dell Laptop',
        itemDescription: 'Business laptop with Windows 11 Pro, 16GB RAM, 512GB SSD',
        quantity: '3',
        unit: 'pcs',
        estimatedUnitPrice: '450000',
        category: 'IT Equipment',
        urgentDate: '2024-02-15',
        notes: 'Required for new employee onboarding'
      },
      {
        title: 'Office Equipment Purchase',
        description: 'Purchase of laptops and monitors for new employees',
        justification: 'New hires require equipment to perform their duties effectively',
        departmentName: 'IT Department',
        requestorEmail: '<EMAIL>',
        priority: 'high',
        itemName: 'Monitor 24 inch',
        itemDescription: 'LED monitor with HDMI and VGA connections',
        quantity: '3',
        unit: 'pcs',
        estimatedUnitPrice: '120000',
        category: 'IT Equipment',
        urgentDate: '2024-02-15',
        notes: 'Required for new employee onboarding'
      },
      {
        title: 'Monthly Office Supplies',
        description: 'Regular monthly replenishment of office supplies',
        justification: 'Essential supplies for daily operations',
        departmentName: 'Administration',
        requestorEmail: '<EMAIL>',
        priority: 'medium',
        itemName: 'A4 Paper',
        itemDescription: 'White A4 printing paper, 80gsm',
        quantity: '50',
        unit: 'ream',
        estimatedUnitPrice: '3500',
        category: 'Office Supplies',
        urgentDate: '',
        notes: 'Monthly requirement'
      },
      {
        title: 'Monthly Office Supplies',
        description: 'Regular monthly replenishment of office supplies',
        justification: 'Essential supplies for daily operations',
        departmentName: 'Administration',
        requestorEmail: '<EMAIL>',
        priority: 'medium',
        itemName: 'Printer Ink Cartridges',
        itemDescription: 'Black and color ink cartridges for HP printers',
        quantity: '10',
        unit: 'set',
        estimatedUnitPrice: '8500',
        category: 'Office Supplies',
        urgentDate: '',
        notes: 'Monthly requirement'
      },
      {
        title: 'Training Materials',
        description: 'Educational materials for teacher training program',
        justification: 'Required for upcoming teacher development workshop',
        departmentName: 'Training Department',
        requestorEmail: '<EMAIL>',
        priority: 'high',
        itemName: 'Training Manuals',
        itemDescription: 'Printed training manuals for teacher development',
        quantity: '100',
        unit: 'copy',
        estimatedUnitPrice: '2500',
        category: 'Educational Materials',
        urgentDate: '2024-02-10',
        notes: 'Urgent for upcoming workshop'
      }
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(sampleData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 30 }, // title
      { wch: 40 }, // description
      { wch: 40 }, // justification
      { wch: 20 }, // departmentName
      { wch: 25 }, // requestorEmail
      { wch: 12 }, // priority
      { wch: 25 }, // itemName
      { wch: 40 }, // itemDescription
      { wch: 10 }, // quantity
      { wch: 8 },  // unit
      { wch: 15 }, // estimatedUnitPrice
      { wch: 20 }, // category
      { wch: 12 }, // urgentDate
      { wch: 30 }  // notes
    ];
    worksheet['!cols'] = columnWidths;

    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Requisitions');

    // Create instructions sheet
    const instructions = [
      {
        Field: 'title',
        Description: 'Requisition title (required)',
        Example: 'Office Equipment Purchase',
        Notes: 'Brief descriptive title for the requisition'
      },
      {
        Field: 'description',
        Description: 'Detailed description (required)',
        Example: 'Purchase of laptops and monitors for new employees',
        Notes: 'Detailed description of what is being requested'
      },
      {
        Field: 'justification',
        Description: 'Business justification',
        Example: 'New hires require equipment to perform duties',
        Notes: 'Why this purchase is necessary'
      },
      {
        Field: 'departmentName',
        Description: 'Department name (required)',
        Example: 'IT Department',
        Notes: 'Must match existing department name'
      },
      {
        Field: 'requestorEmail',
        Description: 'Requestor email (required)',
        Example: '<EMAIL>',
        Notes: 'Email of the person making the request'
      },
      {
        Field: 'priority',
        Description: 'Priority level (required)',
        Example: 'high, medium, low',
        Notes: 'Priority level for the requisition'
      },
      {
        Field: 'itemName',
        Description: 'Item name (required)',
        Example: 'Dell Laptop',
        Notes: 'Name of the item being requested'
      },
      {
        Field: 'itemDescription',
        Description: 'Item description',
        Example: 'Business laptop with Windows 11 Pro',
        Notes: 'Detailed specifications of the item'
      },
      {
        Field: 'quantity',
        Description: 'Quantity (required)',
        Example: '3',
        Notes: 'Must be a positive number'
      },
      {
        Field: 'unit',
        Description: 'Unit of measurement',
        Example: 'pcs, ream, set, box',
        Notes: 'Unit for the quantity'
      },
      {
        Field: 'estimatedUnitPrice',
        Description: 'Estimated unit price in MWK (required)',
        Example: '450000',
        Notes: 'Estimated price per unit in Malawi Kwacha'
      },
      {
        Field: 'category',
        Description: 'Item category',
        Example: 'IT Equipment, Office Supplies',
        Notes: 'Category classification for the item'
      },
      {
        Field: 'urgentDate',
        Description: 'Urgent date needed',
        Example: '2024-02-15',
        Notes: 'Format: YYYY-MM-DD, when item is urgently needed'
      },
      {
        Field: 'notes',
        Description: 'Additional notes',
        Example: 'Required for new employee onboarding',
        Notes: 'Any additional information or special requirements'
      }
    ];

    const instructionsSheet = XLSX.utils.json_to_sheet(instructions);
    instructionsSheet['!cols'] = [
      { wch: 20 }, // Field
      { wch: 30 }, // Description
      { wch: 35 }, // Example
      { wch: 40 }  // Notes
    ];
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Create grouping instructions sheet
    const groupingInstructions = [
      {
        'Import Logic': 'Items with the same title and requestor email will be grouped into a single requisition'
      },
      {
        'Import Logic': 'Each row represents one line item in the requisition'
      },
      {
        'Import Logic': 'Multiple items for the same requisition will create one requisition with multiple line items'
      },
      {
        'Import Logic': 'Different titles or different requestors will create separate requisitions'
      },
      {
        'Import Logic': 'Department names and requestor emails must exist in the system'
      },
      {
        'Import Logic': 'All required fields must be filled for successful import'
      },
      {
        'Import Logic': 'Invalid rows will be reported in the import results'
      },
      {
        'Import Logic': 'Priority must be one of: high, medium, low'
      },
      {
        'Import Logic': 'Estimated unit prices should be in Malawi Kwacha (MWK)'
      },
      {
        'Import Logic': 'Urgent dates should be in YYYY-MM-DD format'
      }
    ];

    const groupingSheet = XLSX.utils.json_to_sheet(groupingInstructions);
    groupingSheet['!cols'] = [{ wch: 80 }];
    XLSX.utils.book_append_sheet(workbook, groupingSheet, 'Import Logic');

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx' 
    });

    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    headers.set('Content-Disposition', 'attachment; filename="requisition_import_template.xlsx"');
    headers.set('Content-Length', excelBuffer.length.toString());

    return new NextResponse(excelBuffer, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('Error generating requisition template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    );
  }
}
