import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Supplier History/Audit Trail Schema
const SupplierHistorySchema = new mongoose.Schema({
  supplierId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true,
    index: true
  },
  action: {
    type: String,
    enum: [
      'created',
      'updated',
      'status_changed',
      'rating_updated',
      'contract_added',
      'contract_renewed',
      'contract_terminated',
      'document_uploaded',
      'document_verified',
      'performance_review',
      'payment_made',
      'order_placed',
      'delivery_received',
      'issue_reported',
      'issue_resolved',
      'note_added'
    ],
    required: true,
    index: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  details: {
    type: mongoose.Schema.Types.Mixed, // Flexible object for action-specific data
    default: {}
  },
  previousValues: {
    type: mongoose.Schema.Types.Mixed, // Store previous values for updates
    default: {}
  },
  newValues: {
    type: mongoose.Schema.Types.Mixed, // Store new values for updates
    default: {}
  },
  performedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  performedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  category: {
    type: String,
    enum: ['administrative', 'financial', 'operational', 'compliance', 'performance'],
    default: 'administrative',
    index: true
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  relatedEntityType: String, // e.g., 'Contract', 'PurchaseOrder', 'Document'
  relatedEntityId: mongoose.Schema.Types.ObjectId,
  ipAddress: String,
  userAgent: String,
  sessionId: String
}, {
  timestamps: true
});

// Create indexes for better query performance
SupplierHistorySchema.index({ supplierId: 1, performedAt: -1 });
SupplierHistorySchema.index({ action: 1, category: 1 });
SupplierHistorySchema.index({ performedBy: 1, performedAt: -1 });

const SupplierHistory = mongoose.models.SupplierHistory || 
  mongoose.model('SupplierHistory', SupplierHistorySchema);

/**
 * GET /api/procurement/suppliers/[id]/history
 * Get audit trail/history for a specific supplier
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_HISTORY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view supplier history.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_HISTORY_FORBIDDEN',
        'Insufficient permissions to view supplier history',
        'You do not have permission to view supplier history.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve params
    const { id } = await params;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const action = searchParams.get('action');
    const category = searchParams.get('category');
    const severity = searchParams.get('severity');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build filters
    const filters: any = { supplierId: id };
    
    if (action) {
      filters.action = action;
    }
    
    if (category) {
      filters.category = category;
    }
    
    if (severity) {
      filters.severity = severity;
    }

    if (startDate || endDate) {
      filters.performedAt = {};
      if (startDate) {
        filters.performedAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filters.performedAt.$lte = new Date(endDate);
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get history records
    const [history, total] = await Promise.all([
      SupplierHistory.find(filters)
        .populate('performedBy', 'firstName lastName email role')
        .sort({ performedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      SupplierHistory.countDocuments(filters)
    ]);

    const pages = Math.ceil(total / limit);

    // Get summary statistics
    const summaryPipeline = [
      { $match: { supplierId: new mongoose.Types.ObjectId(id) } },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 },
          lastOccurrence: { $max: '$performedAt' }
        }
      },
      { $sort: { count: -1 } }
    ];

    const actionSummary = await SupplierHistory.aggregate(summaryPipeline);

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentActivityCount = await SupplierHistory.countDocuments({
      supplierId: id,
      performedAt: { $gte: thirtyDaysAgo }
    });

    return NextResponse.json({
      success: true,
      data: history,
      pagination: {
        page,
        limit,
        total,
        pages
      },
      summary: {
        totalActions: total,
        recentActivity: recentActivityCount,
        actionBreakdown: actionSummary
      }
    });

  } catch (error) {
    logger.error('Error getting supplier history', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_HISTORY_ERROR',
      error instanceof Error ? error.message : 'Failed to get supplier history',
      'Unable to retrieve supplier history.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * POST /api/procurement/suppliers/[id]/history
 * Add a new history entry for a supplier
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_HISTORY_ADD_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to add supplier history.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_HISTORY_ADD_FORBIDDEN',
        'Insufficient permissions to add supplier history',
        'You do not have permission to add supplier history.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve params
    const { id } = await params;

    // Parse request body
    const body = await request.json();
    const {
      action,
      description,
      details = {},
      previousValues = {},
      newValues = {},
      category = 'administrative',
      severity = 'medium',
      relatedEntityType,
      relatedEntityId
    } = body;

    if (!action || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: action, description' },
        { status: 400 }
      );
    }

    // Create history entry
    const historyEntry = new SupplierHistory({
      supplierId: id,
      action,
      description,
      details,
      previousValues,
      newValues,
      performedBy: user.id,
      category,
      severity,
      relatedEntityType,
      relatedEntityId,
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    });

    await historyEntry.save();

    logger.info('Supplier history entry added', LogCategory.PROCUREMENT, {
      supplierId: id,
      action,
      performedBy: user.id,
      historyId: historyEntry._id
    });

    return NextResponse.json({
      success: true,
      data: historyEntry,
      message: 'History entry added successfully'
    });

  } catch (error) {
    logger.error('Error adding supplier history', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_HISTORY_ADD_ERROR',
      error instanceof Error ? error.message : 'Failed to add supplier history',
      'Unable to add supplier history entry.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
