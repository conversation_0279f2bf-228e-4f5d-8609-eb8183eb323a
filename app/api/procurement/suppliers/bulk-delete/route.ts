import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/procurement/SupplierService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

const supplierService = new SupplierService();
const auditService = new AuditDeletionService();

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  supplierIds: z.array(z.string()).min(1, 'At least one supplier ID is required'),
  deletionReason: z.string().min(25, 'Deletion reason must be at least 25 characters'),
  context: z.object({
    department: z.string(),
    fiscalYear: z.string()
  }).optional()
});

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors: Array<{ supplierId: string; message: string }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only managers and admins can bulk delete
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_DELETE_FORBIDDEN',
        'Insufficient permissions to delete suppliers',
        'You do not have permission to delete supplier records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse and validate request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    const validationResult = bulkDeleteSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { supplierIds, deletionReason, context } = validationResult.data;

    // Initialize result tracking
    const result: DeleteResult = {
      requestedCount: supplierIds.length,
      deletedCount: 0,
      auditRecordsCreated: 0,
      errors: []
    };

    // Process each supplier deletion
    for (const supplierId of supplierIds) {
      try {
        // Get supplier details for audit
        const supplier = await supplierService.findById(supplierId);
        if (!supplier) {
          result.errors.push({
            supplierId,
            message: 'Supplier not found'
          });
          continue;
        }

        // Check if supplier has active dependencies
        // Note: This would need to be implemented based on your business logic
        // For example, check for active purchase orders, contracts, etc.
        
        // Create audit record before deletion
        const auditRecord = await auditService.createDeletionRecord({
          entityType: 'Supplier',
          entityId: supplierId,
          entityData: {
            supplierId: supplier.supplierId,
            name: supplier.name,
            category: supplier.category,
            status: supplier.status,
            contactPerson: supplier.contactPerson,
            email: supplier.email,
            phone: supplier.phone,
            address: supplier.address,
            city: supplier.city,
            country: supplier.country
          },
          deletionReason,
          deletedBy: user.id,
          context: {
            department: context?.department || 'Procurement',
            fiscalYear: context?.fiscalYear || new Date().getFullYear().toString(),
            bulkOperation: true,
            totalSuppliers: supplierIds.length
          }
        });

        // Delete the supplier
        await supplierService.deleteSupplier(supplierId);

        result.deletedCount++;
        result.auditRecordsCreated++;

        logger.info('Supplier deleted in bulk operation', LogCategory.PROCUREMENT, {
          supplierId: supplier.supplierId,
          supplierName: supplier.name,
          deletedBy: user.id,
          auditRecordId: auditRecord._id,
          reason: deletionReason
        });

      } catch (error) {
        logger.error(`Error deleting supplier ${supplierId}`, LogCategory.PROCUREMENT, error);
        result.errors.push({
          supplierId,
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    // Log bulk operation summary
    logger.info('Supplier bulk delete operation completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      requestedCount: result.requestedCount,
      deletedCount: result.deletedCount,
      errorCount: result.errors.length,
      auditRecordsCreated: result.auditRecordsCreated,
      deletionReason
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Bulk delete completed. ${result.deletedCount} suppliers deleted successfully.`
    });

  } catch (error) {
    logger.error('Error in supplier bulk delete', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_BULK_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete suppliers',
      'Unable to complete the bulk delete operation. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if suppliers have active dependencies',
        'Contact support if the problem persists'
      ]
    );
  }
}
