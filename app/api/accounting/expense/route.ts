// app/api/accounting/expense/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expense from '@/models/accounting/Expense';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import BudgetSubcategory from '@/models/accounting/BudgetSubcategory';
import mongoose from 'mongoose';
import { budgetTransactionService } from '@/lib/services/accounting/budget-transaction-service';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}


export const runtime = 'nodejs';



// Enhanced expense schema for validation (matching Income model)
const expenseSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional().nullable(),
  amount: z.number().positive("Amount must be positive"),
  reference: z.string().min(2, "Reference must be at least 2 characters"),
  description: z.string().optional().nullable(),
  fiscalYear: z.string().min(4, "Fiscal year is required"),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled']).default('draft'),
  paymentMethod: z.string().optional().nullable(),
  bankAccount: z.string().optional().nullable(),
  budget: z.string().optional().nullable(),
  budgetCategory: z.string().optional().nullable(),
  budgetSubcategory: z.string().optional().nullable(),
  appliedToBudget: z.boolean().optional().default(true),
  notes: z.string().optional().nullable(),

  // Enhanced fields (matching Income model)
  vendor: z.string().optional().nullable(),
  department: z.string().optional().nullable(),
  costCenter: z.string().optional().nullable(),
  rejectionReason: z.string().optional().nullable(),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const fiscalYear = searchParams.get('fiscalYear');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // If ID is provided, get a specific expense
    if (id) {
      const expense = await Expense.findById(id)
        .populate('createdBy', 'name')
        .populate('budgetCategory', 'name type')
        .populate('budgetSubcategory', 'name')
        .populate('bankAccount', 'name accountNumber');

      if (!expense) {
        return NextResponse.json(
          { error: 'Expense not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ expense });
    }

    // Build filter for listing expenses
    // Define MongoDB filter interface
    interface MongoFilter {
      [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
      date?: { $gte?: Date; $lte?: Date };
      amount?: { $gte?: number; $lte?: number };
    }

    const filter: MongoFilter = {};

    // Add category filter if provided
    if (category) {
      filter.category = category;
    }

    // Add status filter if provided
    if (status) {
      filter.status = status;
    }

    // Add fiscal year filter if provided
    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Add date range filters if provided
    if (startDate) {
      filter.date = { $gte: new Date(startDate) };
    }

    if (endDate) {
      if (filter.date) {
        filter.date.$lte = new Date(endDate);
      } else {
        filter.date = { $lte: new Date(endDate) };
      }
    }

    // Get expenses with pagination
    const totalCount = await Expense.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const skip = (page - 1) * limit;

    const expenses = await Expense.find(filter)
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .populate('bankAccount', 'name accountNumber');

    // Get expense summary by category
    const summary = await Expense.aggregate([
      { $match: { fiscalYear: fiscalYear || { $exists: true } } },
      { $group: { _id: '$category', total: { $sum: '$amount' } } }
    ]);

    // Format summary data
    const expenseData = summary.map(item => {
      return {
        name: item._id,
        value: item.total,
        category: item._id
      };
    });

    // Calculate total expenses
    const totalExpenses = expenseData.reduce((sum, item) => sum + item.value, 0);

    // Return expense data
    return NextResponse.json({
      expenses,
      expenseData,
      totalExpenses,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching expense data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const rawData = await req.json();

    // Sanitize data to prevent ObjectId validation errors
    const sanitizeObjectId = (value: any) => {
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        return undefined;
      }
      return value;
    };

    // Clean the data before validation
    const sanitizedData = {
      ...rawData,
      budget: sanitizeObjectId(rawData.budget),
      budgetCategory: sanitizeObjectId(rawData.budgetCategory),
      budgetSubcategory: sanitizeObjectId(rawData.budgetSubcategory),
      bankAccount: sanitizeObjectId(rawData.bankAccount),
      // Also clean other optional string fields
      subcategory: rawData.subcategory || undefined,
      paymentMethod: rawData.paymentMethod || undefined,
      vendor: rawData.vendor || undefined,
      department: rawData.department || undefined,
      costCenter: rawData.costCenter || undefined,
      description: rawData.description || undefined,
      notes: rawData.notes || undefined,
    };

    console.log('Sanitized expense data:', sanitizedData);

    // Validate request body
    const validationResult = expenseSchema.safeParse(sanitizedData);
    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: validationResult.error.errors[0].message,
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Add created by
    const expenseData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create expense
    const expense = await Expense.create(expenseData);

    // Enhanced budget integration (similar to Income API)
    if (expense.budget && expense.budgetCategory && expense.appliedToBudget) {
      try {
        // Validate budget and category before linking
        const budget = await Budget.findById(expense.budget);
        if (!budget) {
          throw new Error(`Budget with ID ${expense.budget} not found`);
        }

        // Check if budget is active
        if (budget.status !== 'active' && budget.status !== 'approved') {
          throw new Error(`Budget ${budget.name} is not active or approved`);
        }

        // Check if expense date is within budget period
        const expenseDate = new Date(expense.date);
        if (budget.startDate && budget.endDate) {
          const startDate = new Date(budget.startDate);
          const endDate = new Date(budget.endDate);

          if (expenseDate < startDate || expenseDate > endDate) {
            throw new Error(`Expense date ${expense.date} is outside the budget period (${budget.startDate} to ${budget.endDate})`);
          }
        }

        // Check if fiscal year matches
        if (budget.fiscalYear !== expense.fiscalYear) {
          throw new Error(`Expense fiscal year ${expense.fiscalYear} does not match budget fiscal year ${budget.fiscalYear}`);
        }

        // Validate category
        const category = await BudgetCategory.findById(expense.budgetCategory);
        if (!category) {
          throw new Error(`Category with ID ${expense.budgetCategory} not found`);
        }

        // Check if category belongs to budget
        if (category.budget.toString() !== expense.budget.toString()) {
          throw new Error(`Category ${category.name} does not belong to the selected budget`);
        }

        // Check if category is expense type
        if (category.type !== 'expense') {
          throw new Error(`Category ${category.name} is not an expense category`);
        }

        // Check if expense would exceed budget allocation
        if (category.budgetedAmount > 0) {
          // Get current actual amount for this category
          const actualResult = await Expense.aggregate([
            {
              $match: {
                budgetCategory: new mongoose.Types.ObjectId(expense.budgetCategory as string),
                status: 'paid',
                appliedToBudget: true,
                _id: { $ne: expense._id } // Exclude current expense
              }
            },
            {
              $group: {
                _id: null,
                total: { $sum: '$amount' }
              }
            }
          ]);

          const currentActual = actualResult.length > 0 ? actualResult[0].total : 0;
          const newTotal = currentActual + expense.amount;

          // Check if this would exceed the budget
          if (newTotal > category.budgetedAmount) {
            logger.warn('Expense would exceed budget allocation', {
              expenseId: expense._id,
              categoryId: expense.budgetCategory,
              budgeted: category.budgetedAmount,
              currentActual,
              newAmount: expense.amount,
              newTotal,
              overage: newTotal - category.budgetedAmount
            });
            // We're not throwing an error here, just logging a warning
            // In a production environment, you might want to implement an approval workflow
          }
        }

        // Validate subcategory if provided
        if (expense.budgetSubcategory) {
          const subcategory = await BudgetSubcategory.findById(expense.budgetSubcategory);
          if (!subcategory) {
            throw new Error(`Subcategory with ID ${expense.budgetSubcategory} not found`);
          }

          // Check if subcategory belongs to category
          if (subcategory.parentCategory.toString() !== expense.budgetCategory.toString()) {
            throw new Error(`Subcategory ${subcategory.name} does not belong to the selected category`);
          }
        }

        // Use the budget transaction service to link expense to budget (for paid expenses)
        if (expense.status === 'paid') {
          await budgetTransactionService.linkExpenseToBudget(
            expense._id.toString(),
            expense.budget.toString(),
            expense.budgetCategory.toString(),
            expense.budgetSubcategory ? expense.budgetSubcategory.toString() : undefined
          );
        }

        // Create BudgetExpenditure record for all statuses (mirrors Income behavior)
        await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expense);

        logger.info('Expense linked to budget successfully', {
          expenseId: expense._id,
          budgetId: expense.budget,
          categoryId: expense.budgetCategory,
          status: expense.status
        });
      } catch (error) {
        logger.error('Error linking expense to budget', error);
        // Continue even if budget update fails, but log the error
        // In a production environment, you might want to handle this differently
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Expense recorded successfully',
        expense
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error recording expense', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const rawData = await req.json();

    // Ensure ID is provided
    if (!rawData.id) {
      return NextResponse.json(
        { error: 'Expense ID is required' },
        { status: 400 }
      );
    }

    // Sanitize data to prevent ObjectId validation errors
    const sanitizeObjectId = (value: any) => {
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        return undefined;
      }
      return value;
    };

    // Extract ID and sanitize the rest of the data
    const { id, ...rawExpenseData } = rawData;
    const sanitizedExpenseData = {
      ...rawExpenseData,
      budget: sanitizeObjectId(rawExpenseData.budget),
      budgetCategory: sanitizeObjectId(rawExpenseData.budgetCategory),
      budgetSubcategory: sanitizeObjectId(rawExpenseData.budgetSubcategory),
      bankAccount: sanitizeObjectId(rawExpenseData.bankAccount),
      // Also clean other optional string fields
      subcategory: rawExpenseData.subcategory || undefined,
      paymentMethod: rawExpenseData.paymentMethod || undefined,
      vendor: rawExpenseData.vendor || undefined,
      department: rawExpenseData.department || undefined,
      costCenter: rawExpenseData.costCenter || undefined,
      description: rawExpenseData.description || undefined,
      notes: rawExpenseData.notes || undefined,
    };

    // Validate the request data (excluding id)
    const validationResult = expenseSchema.partial().safeParse(sanitizedExpenseData);

    if (!validationResult.success) {
      console.error('Validation error in PUT:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: validationResult.error.errors[0].message,
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Get the existing expense
    const existingExpense = await Expense.findById(id);
    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Track status changes for enhanced workflow
    const oldStatus = existingExpense.status;
    const statusChangingToPaid = oldStatus !== 'paid' && validationResult.data.status === 'paid';
    const statusChangingToApproved = oldStatus !== 'approved' && validationResult.data.status === 'approved';

    // Update expense with enhanced status tracking
    const updateData = {
      ...validationResult.data,
      updatedBy: user.id
    };

    // Add status timestamps based on status changes
    if (validationResult.data.status && validationResult.data.status !== oldStatus) {
      switch (validationResult.data.status) {
        case 'approved':
          updateData.approvedAt = new Date();
          updateData.approvedBy = user.id;
          break;
        case 'paid':
          updateData.paidAt = new Date();
          updateData.paidBy = user.id;
          break;
        case 'rejected':
          updateData.rejectedAt = new Date();
          updateData.rejectedBy = user.id;
          break;
        case 'cancelled':
          updateData.cancelledAt = new Date();
          updateData.cancelledBy = user.id;
          break;
      }
    }

    // Update expense
    const updatedExpense = await Expense.findByIdAndUpdate(id, updateData, { new: true });

    // Enhanced budget integration (similar to Income API)
    if (updatedExpense.budget && updatedExpense.budgetCategory && updatedExpense.appliedToBudget) {
      try {
        // Validate budget and category before linking
        const budget = await Budget.findById(updatedExpense.budget);
        if (!budget) {
          throw new Error(`Budget with ID ${updatedExpense.budget} not found`);
        }

        // Check if budget is active
        if (budget.status !== 'active' && budget.status !== 'approved') {
          throw new Error(`Budget ${budget.name} is not active or approved`);
        }

        // Check if expense date is within budget period
        const expenseDate = new Date(updatedExpense.date);
        if (budget.startDate && budget.endDate) {
          const startDate = new Date(budget.startDate);
          const endDate = new Date(budget.endDate);

          if (expenseDate < startDate || expenseDate > endDate) {
            throw new Error(`Expense date ${updatedExpense.date} is outside the budget period (${budget.startDate} to ${budget.endDate})`);
          }
        }

        // Check if fiscal year matches
        if (budget.fiscalYear !== updatedExpense.fiscalYear) {
          throw new Error(`Expense fiscal year ${updatedExpense.fiscalYear} does not match budget fiscal year ${budget.fiscalYear}`);
        }

        // Validate category
        const category = await BudgetCategory.findById(updatedExpense.budgetCategory);
        if (!category) {
          throw new Error(`Category with ID ${updatedExpense.budgetCategory} not found`);
        }

        // Check if category belongs to budget
        if (category.budget.toString() !== updatedExpense.budget.toString()) {
          throw new Error(`Category ${category.name} does not belong to the selected budget`);
        }

        // Check if category is expense type
        if (category.type !== 'expense') {
          throw new Error(`Category ${category.name} is not an expense category`);
        }

        // Check if expense would exceed budget allocation
        if (category.budgetedAmount > 0) {
          // Get current actual amount for this category
          const actualResult = await Expense.aggregate([
            {
              $match: {
                budgetCategory: new mongoose.Types.ObjectId(updatedExpense.budgetCategory as string),
                status: 'paid',
                appliedToBudget: true,
                _id: { $ne: updatedExpense._id } // Exclude current expense
              }
            },
            {
              $group: {
                _id: null,
                total: { $sum: '$amount' }
              }
            }
          ]);

          const currentActual = actualResult.length > 0 ? actualResult[0].total : 0;
          const newTotal = currentActual + updatedExpense.amount;

          // Check if this would exceed the budget
          if (newTotal > category.budgetedAmount) {
            logger.warn('Expense would exceed budget allocation after update', {
              expenseId: updatedExpense._id,
              categoryId: updatedExpense.budgetCategory,
              budgeted: category.budgetedAmount,
              currentActual,
              newAmount: updatedExpense.amount,
              newTotal,
              overage: newTotal - category.budgetedAmount
            });
            // We're not throwing an error here, just logging a warning
            // In a production environment, you might want to implement an approval workflow
          }
        }

        // Validate subcategory if provided
        if (updatedExpense.budgetSubcategory) {
          const subcategory = await BudgetSubcategory.findById(updatedExpense.budgetSubcategory);
          if (!subcategory) {
            throw new Error(`Subcategory with ID ${updatedExpense.budgetSubcategory} not found`);
          }

          // Check if subcategory belongs to category
          if (subcategory.parentCategory.toString() !== updatedExpense.budgetCategory.toString()) {
            throw new Error(`Subcategory ${subcategory.name} does not belong to the selected category`);
          }
        }

        // Use the budget transaction service to link expense to budget (for paid expenses)
        if (statusChangingToPaid) {
          await budgetTransactionService.linkExpenseToBudget(
            updatedExpense._id.toString(),
            updatedExpense.budget.toString(),
            updatedExpense.budgetCategory.toString(),
            updatedExpense.budgetSubcategory ? updatedExpense.budgetSubcategory.toString() : undefined
          );
        }

        // Update BudgetExpenditure record for status changes
        if (validationResult.data.status && validationResult.data.status !== oldStatus) {
          await budgetExpenditureIntegrationService.handleExpenditureStatusChange(updatedExpense, oldStatus);
        } else {
          // Update BudgetExpenditure record for other changes
          await budgetExpenditureIntegrationService.updateExpenditureAsBudgetItem(updatedExpense);
        }

        logger.info('Expense linked to budget successfully after update', {
          expenseId: updatedExpense._id,
          budgetId: updatedExpense.budget,
          categoryId: updatedExpense.budgetCategory,
          oldStatus,
          newStatus: updatedExpense.status
        });
      } catch (error) {
        logger.error('Error linking expense to budget after update', error);
        // Continue even if budget update fails, but log the error
        // In a production environment, you might want to handle this differently
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Expense updated successfully',
      expense: updatedExpense
    });
  } catch (error: unknown) {
    logger.error('Error updating expense', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
