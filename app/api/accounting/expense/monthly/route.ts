// app/api/accounting/expense/monthly/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/utils/logger';
import Expense from '@/models/accounting/Expense';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/expense/monthly
 * Get monthly expenditure data
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';
    const budgetId = searchParams.get('budgetId');

    logger.info('Fetching monthly expenditure data', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear,
      budgetId
    });

    // Get fiscal year date range
    const { startDate, endDate } = getFiscalYearDateRange(fiscalYear);

    // Build query for expenses
    const expenseQuery: any = {
      fiscalYear,
      date: {
        $gte: startDate,
        $lte: endDate
      }
    };

    if (budgetId) {
      expenseQuery.budget = budgetId;
    }

    // Fetch expense data
    const expenses = await Expense.find(expenseQuery).lean();

    // Fetch budget expenditure data for comparison
    const budgetExpenditureQuery: any = {};
    if (budgetId) {
      budgetExpenditureQuery.budget = budgetId;
    }
    if (fiscalYear) {
      budgetExpenditureQuery.fiscalYear = fiscalYear;
    }

    const budgetExpenditures = await BudgetExpenditure.find(budgetExpenditureQuery).lean();

    // Create monthly buckets
    const monthlyData: Record<string, {
      month: string;
      amount: number;
      budgeted: number;
      actual: number;
      variance: number;
      utilizationPercentage: number;
      count: number;
    }> = {};

    // Initialize all months in the fiscal year
    const months = generateFiscalYearMonths(fiscalYear);
    months.forEach(month => {
      monthlyData[month] = {
        month,
        amount: 0,
        budgeted: 0,
        actual: 0,
        variance: 0,
        utilizationPercentage: 0,
        count: 0
      };
    });

    // Aggregate expense data by month
    expenses.forEach(expense => {
      const expenseDate = new Date(expense.date);
      const monthKey = expenseDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      
      if (monthlyData[monthKey]) {
        monthlyData[monthKey].amount += expense.amount || 0;
        monthlyData[monthKey].count += 1;
        
        // Only count paid expenses as actual
        if (expense.status === 'paid') {
          monthlyData[monthKey].actual += expense.amount || 0;
        }
      }
    });

    // Add budgeted amounts (distribute evenly across months for now)
    const totalBudgeted = budgetExpenditures.reduce((sum, item) => sum + (item.budgetedAmount || 0), 0);
    const monthlyBudgeted = totalBudgeted / months.length;

    Object.keys(monthlyData).forEach(month => {
      monthlyData[month].budgeted = monthlyBudgeted;
      monthlyData[month].variance = monthlyData[month].actual - monthlyData[month].budgeted;
      monthlyData[month].utilizationPercentage = monthlyData[month].budgeted > 0 
        ? (monthlyData[month].actual / monthlyData[month].budgeted) * 100 
        : 0;
    });

    // Convert to array and sort by date
    const monthlyExpenditure = Object.values(monthlyData).sort((a, b) => {
      const dateA = new Date(a.month + ' 1, ' + fiscalYear.split('-')[0]);
      const dateB = new Date(b.month + ' 1, ' + fiscalYear.split('-')[0]);
      return dateA.getTime() - dateB.getTime();
    });

    // Calculate summary statistics
    const totalAmount = monthlyExpenditure.reduce((sum, month) => sum + month.amount, 0);
    const totalActual = monthlyExpenditure.reduce((sum, month) => sum + month.actual, 0);
    const totalBudgetedAmount = monthlyExpenditure.reduce((sum, month) => sum + month.budgeted, 0);
    const averageMonthly = totalAmount / months.length;
    const averageActual = totalActual / months.length;

    const response = {
      monthlyExpenditure,
      summary: {
        totalAmount,
        totalActual,
        totalBudgeted: totalBudgetedAmount,
        averageMonthly,
        averageActual,
        totalVariance: totalActual - totalBudgetedAmount,
        overallUtilization: totalBudgetedAmount > 0 ? (totalActual / totalBudgetedAmount) * 100 : 0,
        monthsWithData: monthlyExpenditure.filter(m => m.count > 0).length,
        totalTransactions: monthlyExpenditure.reduce((sum, month) => sum + month.count, 0)
      },
      fiscalYear,
      budgetId,
      generatedAt: new Date().toISOString()
    };

    logger.info('Monthly expenditure data generated successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear,
      totalAmount,
      monthsWithData: response.summary.monthsWithData
    });

    return NextResponse.json(response);

  } catch (error: unknown) {
    logger.error('Error fetching monthly expenditure data', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get fiscal year date range
 */
function getFiscalYearDateRange(fiscalYear: string): { startDate: Date; endDate: Date } {
  const [startYear, endYear] = fiscalYear.split('-').map(y => parseInt(y));
  
  // Fiscal year typically runs from April 1 to March 31
  const startDate = new Date(startYear, 3, 1); // April 1
  const endDate = new Date(endYear, 2, 31); // March 31
  
  return { startDate, endDate };
}

/**
 * Helper function to generate all months in a fiscal year
 */
function generateFiscalYearMonths(fiscalYear: string): string[] {
  const [startYear, endYear] = fiscalYear.split('-').map(y => parseInt(y));
  const months: string[] = [];
  
  // April to December of start year
  for (let month = 3; month < 12; month++) { // 3 = April (0-indexed)
    const date = new Date(startYear, month, 1);
    months.push(date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' }));
  }
  
  // January to March of end year
  for (let month = 0; month < 3; month++) { // 0-2 = Jan-Mar
    const date = new Date(endYear, month, 1);
    months.push(date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' }));
  }
  
  return months;
}
