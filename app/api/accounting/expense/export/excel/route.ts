// app/api/accounting/expense/export/excel/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import * as XLSX from 'xlsx';
import Expense from '@/models/accounting/Expense';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/expense/export/excel
 * Export expenditure data to Excel format
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for data export' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const fiscalYear = searchParams.get('fiscalYear');
    const budgetId = searchParams.get('budgetId');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const department = searchParams.get('department');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeFields = searchParams.getAll('includeFields');
    const groupBy = searchParams.get('groupBy');
    const includeSummary = searchParams.get('includeSummary') === 'true';
    const includeCharts = searchParams.get('includeCharts') === 'true';

    logger.info('Starting expenditure Excel export', LogCategory.ACCOUNTING, {
      userId: user.id,
      filters: { fiscalYear, budgetId, status, category, department, startDate, endDate },
      options: { includeFields, groupBy, includeSummary, includeCharts }
    });

    // Build query
    const query: any = {};
    
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetId) query.budget = budgetId;
    if (status) query.status = status;
    if (category) query.category = category;
    if (department) query.department = department;
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Fetch expenditure data
    const expenditures = await Expense.find(query)
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .populate('createdBy', 'email')
      .populate('approvedBy', 'email')
      .populate('paidBy', 'email')
      .sort({ date: -1 })
      .lean();

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Prepare data based on included fields
    const prepareRowData = (expense: any) => {
      const row: any = {};

      if (includeFields.includes('basic') || includeFields.length === 0) {
        row['Date'] = expense.date ? new Date(expense.date).toLocaleDateString() : '';
        row['Reference'] = expense.reference || '';
        row['Category'] = expense.category ? expense.category.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : '';
        row['Subcategory'] = expense.subcategory || '';
        row['Amount (MWK)'] = expense.amount || 0;
        row['Description'] = expense.description || '';
      }

      if (includeFields.includes('budget')) {
        row['Budget'] = expense.budget?.name || '';
        row['Budget Category'] = expense.budgetCategory?.name || '';
        row['Budget Subcategory'] = expense.budgetSubcategory?.name || '';
        row['Applied to Budget'] = expense.appliedToBudget ? 'Yes' : 'No';
      }

      if (includeFields.includes('status')) {
        row['Status'] = expense.status ? expense.status.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : '';
        row['Submitted At'] = expense.submittedAt ? new Date(expense.submittedAt).toLocaleDateString() : '';
        row['Approved At'] = expense.approvedAt ? new Date(expense.approvedAt).toLocaleDateString() : '';
        row['Paid At'] = expense.paidAt ? new Date(expense.paidAt).toLocaleDateString() : '';
      }

      if (includeFields.includes('financial')) {
        row['Payment Method'] = expense.paymentMethod ? expense.paymentMethod.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : '';
        row['Vendor'] = expense.vendor || '';
        row['Department'] = expense.department ? expense.department.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : '';
        row['Cost Center'] = expense.costCenter || '';
        row['Fiscal Year'] = expense.fiscalYear || '';
      }

      if (includeFields.includes('audit')) {
        row['Created By'] = expense.createdBy?.email || '';
        row['Created At'] = expense.createdAt ? new Date(expense.createdAt).toLocaleDateString() : '';
        row['Approved By'] = expense.approvedBy?.email || '';
        row['Paid By'] = expense.paidBy?.email || '';
      }

      row['Notes'] = expense.notes || '';

      return row;
    };

    // Group data if requested
    let worksheetData: any[] = [];
    
    if (groupBy && groupBy !== 'none') {
      const grouped = expenditures.reduce((acc, expense) => {
        let groupKey = '';
        
        switch (groupBy) {
          case 'category':
            groupKey = expense.category || 'Uncategorized';
            break;
          case 'department':
            groupKey = expense.department || 'Unassigned';
            break;
          case 'status':
            groupKey = expense.status || 'Unknown';
            break;
          case 'month':
            groupKey = expense.date ? new Date(expense.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long' }) : 'Unknown';
            break;
          default:
            groupKey = 'All';
        }
        
        if (!acc[groupKey]) acc[groupKey] = [];
        acc[groupKey].push(expense);
        return acc;
      }, {} as Record<string, any[]>);

      // Create grouped data with headers
      Object.entries(grouped).forEach(([groupName, groupExpenses]) => {
        // Add group header
        worksheetData.push({
          'Date': `=== ${groupName.toUpperCase()} (${groupExpenses.length} items) ===`,
          'Reference': '',
          'Category': '',
          'Amount (MWK)': groupExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0)
        });
        
        // Add group data
        groupExpenses.forEach(expense => {
          worksheetData.push(prepareRowData(expense));
        });
        
        // Add spacing
        worksheetData.push({});
      });
    } else {
      worksheetData = expenditures.map(prepareRowData);
    }

    // Create main data worksheet
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
    
    // Set column widths
    const columnWidths = [
      { wch: 12 }, // Date
      { wch: 15 }, // Reference
      { wch: 18 }, // Category
      { wch: 15 }, // Subcategory
      { wch: 15 }, // Amount
      { wch: 30 }, // Description
      { wch: 20 }, // Budget
      { wch: 20 }, // Budget Category
      { wch: 15 }, // Status
      { wch: 15 }, // Payment Method
      { wch: 20 }, // Vendor
      { wch: 15 }, // Department
      { wch: 12 }, // Fiscal Year
      { wch: 25 }  // Notes
    ];
    worksheet['!cols'] = columnWidths;

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Expenditures');

    // Add summary worksheet if requested
    if (includeSummary) {
      const totalAmount = expenditures.reduce((sum, exp) => sum + (exp.amount || 0), 0);
      const statusCounts = expenditures.reduce((acc, exp) => {
        acc[exp.status || 'unknown'] = (acc[exp.status || 'unknown'] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const categoryCounts = expenditures.reduce((acc, exp) => {
        const category = exp.category || 'uncategorized';
        if (!acc[category]) acc[category] = { count: 0, amount: 0 };
        acc[category].count++;
        acc[category].amount += exp.amount || 0;
        return acc;
      }, {} as Record<string, { count: number; amount: number }>);

      const summaryData = [
        ['EXPENDITURE SUMMARY'],
        [''],
        ['Total Records', expenditures.length],
        ['Total Amount (MWK)', totalAmount],
        ['Export Date', new Date().toLocaleDateString()],
        ['Exported By', user.email],
        [''],
        ['STATUS BREAKDOWN'],
        ...Object.entries(statusCounts).map(([status, count]) => [
          status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          count
        ]),
        [''],
        ['CATEGORY BREAKDOWN'],
        ['Category', 'Count', 'Amount (MWK)'],
        ...Object.entries(categoryCounts).map(([category, data]) => [
          category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          data.count,
          data.amount
        ])
      ];

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
      summaryWorksheet['!cols'] = [{ wch: 25 }, { wch: 15 }, { wch: 15 }];
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');
    }

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true 
    });

    logger.info('Expenditure Excel export completed', LogCategory.ACCOUNTING, {
      userId: user.id,
      recordCount: expenditures.length,
      fileSize: excelBuffer.length
    });

    // Return file
    const response = new NextResponse(excelBuffer);
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    response.headers.set('Content-Disposition', `attachment; filename="expenditures_${new Date().toISOString().split('T')[0]}.xlsx"`);
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');

    return response;

  } catch (error: unknown) {
    logger.error('Error in expenditure Excel export', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
