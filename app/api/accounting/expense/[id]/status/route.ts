// app/api/accounting/expense/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expense from '@/models/accounting/Expense';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Status change schema
const statusChangeSchema = z.object({
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled']),
  notes: z.string().optional(),
  reason: z.string().optional(),
});

/**
 * PATCH /api/accounting/expense/[id]/status
 * Update expense status with proper workflow handling
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = statusChangeSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { status, notes, reason } = validationResult.data;

    // Get the existing expense
    const existingExpense = await Expense.findById(id);
    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Store old status for comparison
    const oldStatus = existingExpense.status;

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      'draft': ['pending_approval', 'cancelled'],
      'pending_approval': ['approved', 'rejected', 'cancelled'],
      'approved': ['paid', 'cancelled'],
      'rejected': ['draft', 'cancelled'],
      'paid': ['cancelled'], // Paid expenses can only be cancelled
      'cancelled': [] // Cancelled expenses cannot change status
    };

    if (!validTransitions[oldStatus]?.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status transition from ${oldStatus} to ${status}` },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {
      status,
      updatedBy: user.id
    };

    // Add status-specific fields and timestamps
    switch (status) {
      case 'pending_approval':
        updateData.submittedAt = new Date();
        break;
      case 'approved':
        updateData.approvedAt = new Date();
        updateData.approvedBy = user.id;
        break;
      case 'rejected':
        updateData.rejectedAt = new Date();
        updateData.rejectedBy = user.id;
        if (reason) {
          updateData.rejectionReason = reason;
        }
        break;
      case 'paid':
        updateData.paidAt = new Date();
        updateData.paidBy = user.id;
        break;
      case 'cancelled':
        updateData.cancelledAt = new Date();
        updateData.cancelledBy = user.id;
        break;
    }

    // Add to status history
    const statusHistoryEntry = {
      status,
      changedBy: user.id,
      changedAt: new Date(),
      notes,
      reason
    };

    updateData.$push = {
      statusHistory: statusHistoryEntry
    };

    // Update the expense
    const updatedExpense = await Expense.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('createdBy updatedBy approvedBy rejectedBy paidBy cancelledBy', 'name email');

    if (!updatedExpense) {
      return NextResponse.json(
        { error: 'Failed to update expense' },
        { status: 500 }
      );
    }

    // Handle budget integration for status changes
    if (updatedExpense.appliedToBudget && updatedExpense.budget && updatedExpense.budgetCategory) {
      try {
        await budgetExpenditureIntegrationService.handleExpenditureStatusChange(updatedExpense, oldStatus);
        
        logger.info('Budget integration updated for expense status change', {
          expenseId: updatedExpense._id,
          oldStatus,
          newStatus: status,
          budgetId: updatedExpense.budget,
          categoryId: updatedExpense.budgetCategory
        });
      } catch (error) {
        logger.error('Error updating budget integration for expense status change', error);
        // Continue even if budget update fails, but log the error
      }
    }

    // Log the status change
    logger.info('Expense status updated successfully', {
      expenseId: updatedExpense._id,
      oldStatus,
      newStatus: status,
      changedBy: user.id,
      notes,
      reason
    });

    return NextResponse.json({
      success: true,
      message: `Expense status updated from ${oldStatus} to ${status}`,
      expense: updatedExpense,
      statusChange: {
        oldStatus,
        newStatus: status,
        changedBy: user.name || user.email,
        changedAt: new Date(),
        notes,
        reason
      }
    });

  } catch (error: unknown) {
    logger.error('Error updating expense status', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/accounting/expense/[id]/status
 * Get expense status history
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get the expense with status history
    const expense = await Expense.findById(id)
      .populate('statusHistory.changedBy', 'name email')
      .populate('createdBy updatedBy approvedBy rejectedBy paidBy cancelledBy', 'name email');

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      expense: {
        id: expense._id,
        reference: expense.reference,
        amount: expense.amount,
        currentStatus: expense.status,
        statusHistory: expense.statusHistory || [],
        timestamps: {
          createdAt: expense.createdAt,
          updatedAt: expense.updatedAt,
          submittedAt: expense.submittedAt,
          approvedAt: expense.approvedAt,
          rejectedAt: expense.rejectedAt,
          paidAt: expense.paidAt,
          cancelledAt: expense.cancelledAt
        },
        statusUsers: {
          createdBy: expense.createdBy,
          updatedBy: expense.updatedBy,
          approvedBy: expense.approvedBy,
          rejectedBy: expense.rejectedBy,
          paidBy: expense.paidBy,
          cancelledBy: expense.cancelledBy
        }
      }
    });

  } catch (error: unknown) {
    logger.error('Error fetching expense status history', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
