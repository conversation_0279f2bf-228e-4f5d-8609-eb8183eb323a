// app/api/accounting/income/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import BudgetSubcategory from '@/models/accounting/BudgetSubcategory';
import { budgetTransactionService } from '@/lib/services/accounting/budget-transaction-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Income validation schema for updates
const updateIncomeSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }).optional(),
  source: z.enum(['government_subvention', 'registration_fees', 'licensing_fees', 'donations', 'other']).optional(),
  subSource: z.string().optional(),
  amount: z.number().positive("Amount must be positive").optional(),
  reference: z.string().min(2, "Reference must be at least 2 characters").optional(),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, "Fiscal year is required").optional(),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'received', 'cancelled']).optional(),
  paymentMethod: z.string().optional(),
  bankAccount: z.string().optional(),
  budget: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetSubcategory: z.string().optional(),
  appliedToBudget: z.boolean().optional(),
  notes: z.string().optional(),
});

/**
 * GET /api/accounting/income/[id]
 * Get a specific income transaction by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get income transaction
    const income = await Income.findById(id)
      .populate('createdBy', 'firstName lastName email')
      .populate('budget', 'name fiscalYear status')
      .populate('budgetCategory', 'name type budgetedAmount')
      .populate('budgetSubcategory', 'name budgetedAmount')
      .populate('bankAccount', 'name accountNumber');

    if (!income) {
      return NextResponse.json(
        { error: 'Income transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ income });
  } catch (error: unknown) {
    logger.error('Error fetching income transaction', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/income/[id]
 * Update a specific income transaction
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get existing income transaction
    const existingIncome = await Income.findById(id);
    if (!existingIncome) {
      return NextResponse.json(
        { error: 'Income transaction not found' },
        { status: 404 }
      );
    }

    // Check if user can edit this income (only creator or admin)
    const isCreator = existingIncome.createdBy.toString() === user.id;
    const isAdmin = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);
    
    if (!isCreator && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: You can only edit income transactions you created' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = updateIncomeSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Update income transaction
    const updatedIncome = await Income.findByIdAndUpdate(
      id,
      {
        ...validationResult.data,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email')
     .populate('budget', 'name fiscalYear status')
     .populate('budgetCategory', 'name type budgetedAmount')
     .populate('budgetSubcategory', 'name budgetedAmount');

    // Update budget if budget-related fields changed
    if (updatedIncome.budget && updatedIncome.budgetCategory && updatedIncome.appliedToBudget) {
      try {
        await budgetTransactionService.updateIncomeInBudget(
          updatedIncome._id.toString(),
          updatedIncome.budget.toString(),
          updatedIncome.budgetCategory.toString(),
          updatedIncome.budgetSubcategory ? updatedIncome.budgetSubcategory.toString() : undefined
        );

        logger.info('Budget updated after income modification', {
          incomeId: updatedIncome._id,
          budgetId: updatedIncome.budget,
          categoryId: updatedIncome.budgetCategory
        });
      } catch (error) {
        logger.error('Error updating budget after income modification', error);
        // Continue even if budget update fails, but log the error
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Income transaction updated successfully',
      income: updatedIncome
    });
  } catch (error: unknown) {
    logger.error('Error updating income transaction', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/income/[id]
 * Delete a specific income transaction
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get existing income transaction
    const existingIncome = await Income.findById(id);
    if (!existingIncome) {
      return NextResponse.json(
        { error: 'Income transaction not found' },
        { status: 404 }
      );
    }

    // Check if user can delete this income (only creator or admin)
    const isCreator = existingIncome.createdBy.toString() === user.id;
    const isAdmin = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);
    
    if (!isCreator && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: You can only delete income transactions you created' },
        { status: 403 }
      );
    }

    // Check if income can be deleted (only draft or rejected status)
    if (!['draft', 'rejected', 'cancelled'].includes(existingIncome.status)) {
      return NextResponse.json(
        { error: 'Only draft, rejected, or cancelled income transactions can be deleted' },
        { status: 400 }
      );
    }

    // Remove from budget if it was applied
    if (existingIncome.budget && existingIncome.budgetCategory && existingIncome.appliedToBudget) {
      try {
        await budgetTransactionService.removeIncomeFromBudget(
          existingIncome._id.toString(),
          existingIncome.budget.toString(),
          existingIncome.budgetCategory.toString(),
          existingIncome.budgetSubcategory ? existingIncome.budgetSubcategory.toString() : undefined
        );

        logger.info('Income removed from budget before deletion', {
          incomeId: existingIncome._id,
          budgetId: existingIncome.budget,
          categoryId: existingIncome.budgetCategory
        });
      } catch (error) {
        logger.error('Error removing income from budget before deletion', error);
        // Continue with deletion even if budget update fails
      }
    }

    // Delete the income transaction
    await Income.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Income transaction deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting income transaction', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
