// app/api/accounting/income/simple/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Income from '@/models/accounting/Income';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import budgetIntegrationService from '@/lib/services/accounting/budget-integration-service';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';

export const runtime = 'nodejs';

// Enhanced income validation schema with budget integration
const simpleIncomeSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }),
  source: z.enum(['government_subvention', 'registration_fees', 'licensing_fees', 'donations', 'other']),
  subSource: z.string().optional(),
  amount: z.number().positive("Amount must be positive"),
  reference: z.string().min(2, "Reference must be at least 2 characters"),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, "Fiscal year is required"),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'received', 'cancelled']).default('draft'),
  paymentMethod: z.string().optional(),
  bankAccount: z.string().optional(),
  // Budget integration fields
  budget: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetSubcategory: z.string().optional(),
  appliedToBudget: z.boolean().optional().default(true), // Default to true for automatic integration
  notes: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    console.log('Simple income API called')
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      console.log('Unauthorized access attempt')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('User authenticated:', user.email)

    // Connect to database
    await connectToDatabase();
    console.log('Database connected')

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      console.log('Insufficient permissions for user:', user.email)
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    console.log('Request data received:', data)

    // Validate request body
    const validationResult = simpleIncomeSchema.safeParse(data);
    if (!validationResult.success) {
      console.log('Validation failed:', validationResult.error.errors)
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    console.log('Data validated successfully')

    // Prepare income data
    const incomeData = {
      ...validationResult.data,
      createdBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('Creating income with data:', incomeData)

    // Create income transaction
    const income = await Income.create(incomeData);
    console.log('Income created successfully:', income._id)

    // Automatic budget integration if appliedToBudget is true - NON-BLOCKING
    if (validationResult.data.appliedToBudget) {
      // Run budget integration in background to avoid blocking the response
      Promise.allSettled([
        budgetIntegrationService.handleNewIncome(income),
        budgetIncomeIntegrationService.createIncomeAsBudgetItem(income)
      ]).then(() => {
        console.log('Income applied to budget successfully')
      }).catch((budgetError) => {
        console.warn('Budget integration failed, but income was created:', budgetError)
      });
    }

    // Populate the created income for response
    const populatedIncome = await Income.findById(income._id)
      .populate('createdBy', 'name email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .lean();

    console.log('Income populated and ready to return')

    return NextResponse.json(
      {
        success: true,
        message: 'Income transaction recorded successfully',
        income: populatedIncome
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    console.error('Error in simple income API:', error)
    logger.error('Error recording income transaction (simple)', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
