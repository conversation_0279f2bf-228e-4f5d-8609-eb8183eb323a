// app/api/accounting/income/drafts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import Income from '@/models/accounting/Income';
import { Budget, BudgetCategory, BudgetSubcategory } from '@/models/accounting/Budget';
import User from '@/models/User';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    Budget;
    BudgetCategory;
    BudgetSubcategory;
    User;

    // Check permissions - only specific roles can view drafts
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view draft income' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear');
    const status = searchParams.get('status') || 'draft';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build filter for draft income
    const filter: Record<string, any> = {};

    // Filter by status (default to draft, but allow other pending statuses)
    const allowedStatuses = ['draft', 'pending_approval', 'rejected'];
    if (allowedStatuses.includes(status)) {
      filter.status = status;
    } else {
      filter.status = { $in: allowedStatuses };
    }

    // Add fiscal year filter if provided
    if (fiscalYear && fiscalYear !== 'all') {
      filter.fiscalYear = fiscalYear;
    }

    // Get total count for pagination
    const totalCount = await Income.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);

    // Fetch draft income records
    const draftIncome = await Income.find(filter)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(limit)
      .lean();

    // Get summary statistics
    const summaryStats = await Income.aggregate([
      {
        $match: {
          status: { $in: allowedStatuses },
          ...(fiscalYear && fiscalYear !== 'all' ? { fiscalYear } : {})
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Format summary statistics
    const summary = {
      draft: { count: 0, totalAmount: 0 },
      pending_approval: { count: 0, totalAmount: 0 },
      rejected: { count: 0, totalAmount: 0 }
    };

    summaryStats.forEach(stat => {
      if (summary[stat._id as keyof typeof summary]) {
        summary[stat._id as keyof typeof summary] = {
          count: stat.count,
          totalAmount: stat.totalAmount
        };
      }
    });

    // Calculate totals
    const totalDraftAmount = Object.values(summary).reduce((sum, stat) => sum + stat.totalAmount, 0);
    const totalDraftCount = Object.values(summary).reduce((sum, stat) => sum + stat.count, 0);

    // Get fiscal years for filtering
    const fiscalYears = await Income.distinct('fiscalYear', {
      status: { $in: allowedStatuses }
    });

    return NextResponse.json({
      draftIncome,
      summary,
      totals: {
        count: totalDraftCount,
        amount: totalDraftAmount
      },
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      filters: {
        fiscalYears: fiscalYears.sort().reverse(), // Most recent first
        statuses: [
          { value: 'draft', label: 'Draft', count: summary.draft.count },
          { value: 'pending_approval', label: 'Pending Approval', count: summary.pending_approval.count },
          { value: 'rejected', label: 'Rejected', count: summary.rejected.count }
        ]
      }
    });

  } catch (error: unknown) {
    console.error('Error fetching draft income:', error);
    logger.error('Error fetching draft income', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// POST method for bulk status updates
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    Budget;
    BudgetCategory;
    BudgetSubcategory;
    User;

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk operations' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    const { incomeIds, action, notes } = data;

    if (!incomeIds || !Array.isArray(incomeIds) || incomeIds.length === 0) {
      return NextResponse.json(
        { error: 'Income IDs are required for bulk operations' },
        { status: 400 }
      );
    }

    if (!action || !['approve', 'reject', 'receive', 'cancel'].includes(action)) {
      return NextResponse.json(
        { error: 'Valid action is required (approve, reject, receive, cancel)' },
        { status: 400 }
      );
    }

    // Map actions to statuses
    const statusMap: Record<string, string> = {
      'approve': 'approved',
      'reject': 'rejected',
      'receive': 'received',
      'cancel': 'cancelled'
    };

    const newStatus = statusMap[action];
    const results = [];

    // Process each income record
    for (const incomeId of incomeIds) {
      try {
        const income = await Income.findById(incomeId);
        if (!income) {
          results.push({ incomeId, success: false, error: 'Income not found' });
          continue;
        }

        // Update status
        const previousStatus = income.status;
        income.status = newStatus;
        income.updatedBy = user.id;
        income.updatedAt = new Date();

        // Add to status history
        if (!income.statusHistory) {
          income.statusHistory = [];
        }

        income.statusHistory.push({
          status: newStatus,
          changedBy: user.id,
          changedAt: new Date(),
          notes: notes || `Bulk ${action} operation`,
          reason: action === 'reject' ? (notes || 'Bulk rejection') : ''
        });

        // Set specific timestamps
        switch (newStatus) {
          case 'approved':
            income.approvedAt = new Date();
            income.approvedBy = user.id;
            break;
          case 'received':
            income.receivedAt = new Date();
            income.receivedBy = user.id;
            break;
          case 'rejected':
            income.rejectedAt = new Date();
            income.rejectedBy = user.id;
            income.rejectionReason = notes || 'Bulk rejection';
            break;
          case 'cancelled':
            income.cancelledAt = new Date();
            income.cancelledBy = user.id;
            break;
        }

        await income.save();
        results.push({ incomeId, success: true, previousStatus, newStatus });

      } catch (error) {
        results.push({ 
          incomeId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    // Count successful operations
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    logger.info('Bulk income status update completed', {
      action,
      totalRecords: incomeIds.length,
      successCount,
      failureCount,
      updatedBy: user.id
    });

    return NextResponse.json({
      success: true,
      message: `Bulk ${action} completed: ${successCount} successful, ${failureCount} failed`,
      results,
      summary: {
        total: incomeIds.length,
        successful: successCount,
        failed: failureCount
      }
    });

  } catch (error: unknown) {
    console.error('Error in bulk income status update:', error);
    logger.error('Error in bulk income status update', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
