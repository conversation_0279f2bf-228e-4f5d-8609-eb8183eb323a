// app/api/accounting/income/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import Income from '@/models/accounting/Income';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';
import budgetIntegrationService from '@/lib/services/accounting/budget-integration-service';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

// Income source mapping
const INCOME_SOURCE_MAP: Record<string, string> = {
  'government_subvention': 'government_subvention',
  'government subvention': 'government_subvention',
  'subvention': 'government_subvention',
  'registration_fees': 'registration_fees',
  'registration fees': 'registration_fees',
  'registration': 'registration_fees',
  'licensing_fees': 'licensing_fees',
  'licensing fees': 'licensing_fees',
  'licensing': 'licensing_fees',
  'donations': 'donations',
  'donation': 'donations',
  'other': 'other'
};

// Status mapping
const STATUS_MAP: Record<string, string> = {
  'draft': 'draft',
  'pending_approval': 'pending_approval',
  'pending approval': 'pending_approval',
  'pending': 'pending_approval',
  'approved': 'approved',
  'received': 'received',
  'rejected': 'rejected',
  'cancelled': 'cancelled'
};

interface ImportRow {
  row: number;
  data: any;
  errors: string[];
  income?: any;
}

/**
 * POST /api/accounting/income/bulk-import
 * Bulk import income transactions from CSV/Excel
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get form data
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ 
        error: 'Invalid file type. Please upload a CSV or Excel file' 
      }, { status: 400 });
    }

    // Read file
    const buffer = await file.arrayBuffer();

    logger.info('Processing income bulk import', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    });

    // Parse file
    let workbook: XLSX.WorkBook;
    try {
      if (fileType === 'csv') {
        const csvData = new TextDecoder().decode(buffer);
        workbook = XLSX.read(csvData, { type: 'string' });
      } else {
        workbook = XLSX.read(buffer, { type: 'array' });
      }
    } catch (error) {
      return NextResponse.json({ 
        error: 'Failed to parse file. Please ensure it is a valid CSV or Excel file.' 
      }, { status: 400 });
    }

    // Get first worksheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const rawData = XLSX.utils.sheet_to_json(worksheet, { defval: null });

    if (rawData.length === 0) {
      return NextResponse.json({ 
        error: 'File is empty or has no data rows' 
      }, { status: 400 });
    }

    // Get budgets and categories for validation
    const budgets = await Budget.find({ status: { $in: ['active', 'approved'] } })
      .select('_id name fiscalYear')
      .lean();
    
    const budgetCategories = await BudgetCategory.find({})
      .select('_id name type budget')
      .lean();

    // Create lookup maps
    const budgetMap = new Map(budgets.map(b => [b.name.toLowerCase(), b]));
    const budgetFiscalYearMap = new Map(budgets.map(b => [b.fiscalYear, b]));
    const categoryMap = new Map(budgetCategories.map(c => [c.name.toLowerCase(), c]));

    // Process rows
    const results: ImportRow[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < rawData.length; i++) {
      const rowData = rawData[i] as any;
      const row: ImportRow = {
        row: i + 2, // +2 because Excel rows start at 1 and we skip header
        data: rowData,
        errors: []
      };

      try {
        // Validate required fields
        if (!rowData.date) row.errors.push('Date is required');
        if (!rowData.source) row.errors.push('Source is required');
        if (!rowData.amount) row.errors.push('Amount is required');
        if (!rowData.reference) row.errors.push('Reference is required');
        if (!rowData.fiscalYear) row.errors.push('Fiscal Year is required');

        // Validate and parse data
        let parsedDate: Date | null = null;
        if (rowData.date) {
          parsedDate = new Date(rowData.date);
          if (isNaN(parsedDate.getTime())) {
            row.errors.push('Invalid date format');
          }
        }

        let parsedAmount: number | null = null;
        if (rowData.amount) {
          parsedAmount = parseFloat(rowData.amount.toString().replace(/[,\s]/g, ''));
          if (isNaN(parsedAmount) || parsedAmount <= 0) {
            row.errors.push('Amount must be a positive number');
          }
        }

        // Validate source
        const source = INCOME_SOURCE_MAP[rowData.source?.toLowerCase()];
        if (!source) {
          row.errors.push(`Invalid source. Must be one of: ${Object.keys(INCOME_SOURCE_MAP).join(', ')}`);
        }

        // Validate status
        const status = STATUS_MAP[rowData.status?.toLowerCase()] || 'draft';

        // Validate budget (optional)
        let budget = null;
        if (rowData.budget) {
          budget = budgetMap.get(rowData.budget.toLowerCase()) || 
                   budgetFiscalYearMap.get(rowData.fiscalYear);
          if (!budget) {
            row.errors.push(`Budget '${rowData.budget}' not found`);
          }
        } else if (rowData.fiscalYear) {
          budget = budgetFiscalYearMap.get(rowData.fiscalYear);
        }

        // Validate budget category (optional)
        let budgetCategory = null;
        if (rowData.budgetCategory && budget) {
          budgetCategory = budgetCategories.find(c => 
            c.name.toLowerCase() === rowData.budgetCategory.toLowerCase() &&
            c.budget.toString() === budget._id.toString()
          );
          if (!budgetCategory) {
            row.errors.push(`Budget category '${rowData.budgetCategory}' not found for budget '${budget.name}'`);
          }
        }

        // If no errors, create income data
        if (row.errors.length === 0) {
          row.income = {
            date: parsedDate,
            source,
            amount: parsedAmount,
            reference: rowData.reference,
            description: rowData.description || '',
            fiscalYear: rowData.fiscalYear,
            status,
            budget: budget?._id,
            budgetCategory: budgetCategory?._id,
            appliedToBudget: !!(budget && budgetCategory),
            notes: rowData.notes || '',
            createdBy: user.id
          };
          successCount++;
        } else {
          errorCount++;
        }

      } catch (error) {
        row.errors.push(`Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        errorCount++;
      }

      results.push(row);
    }

    // If there are errors, return validation results without importing
    if (errorCount > 0) {
      return NextResponse.json({
        success: false,
        message: `Validation failed. ${errorCount} rows have errors.`,
        totalRows: rawData.length,
        successCount: 0,
        errorCount,
        errors: results.filter(r => r.errors.length > 0).map(r => ({
          row: r.row,
          errors: r.errors,
          data: r.data
        }))
      }, { status: 400 });
    }

    // Import valid records
    const importedIncomes = [];
    const importErrors = [];

    for (const result of results) {
      if (result.income) {
        try {
          const income = await Income.create(result.income);
          importedIncomes.push(income);

          // Run budget integration in background
          if (income.appliedToBudget) {
            Promise.allSettled([
              budgetIntegrationService.handleNewIncome(income),
              budgetIncomeIntegrationService.createIncomeAsBudgetItem(income)
            ]).catch(error => {
              logger.error('Background budget integration failed during import', {
                incomeId: income._id,
                error: error.message
              });
            });
          }

        } catch (error) {
          importErrors.push({
            row: result.row,
            error: error instanceof Error ? error.message : 'Failed to create income',
            data: result.data
          });
        }
      }
    }

    logger.info('Income bulk import completed', {
      userId: user.id,
      fileName: file.name,
      totalRows: rawData.length,
      imported: importedIncomes.length,
      errors: importErrors.length
    });

    return NextResponse.json({
      success: true,
      message: `Successfully imported ${importedIncomes.length} income transactions`,
      totalRows: rawData.length,
      successCount: importedIncomes.length,
      errorCount: importErrors.length,
      errors: importErrors,
      importedIncomes: importedIncomes.map(income => ({
        id: income._id,
        reference: income.reference,
        amount: income.amount,
        source: income.source,
        status: income.status
      }))
    });

  } catch (error: unknown) {
    logger.error('Error in income bulk import', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
