// app/api/accounting/cost-centers/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { costCenterService } from '@/lib/services/accounting/cost-center-service';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for updating cost center
const updateCostCenterSchema = z.object({
  code: z.string().min(2, 'Code must be at least 2 characters long').max(10, 'Code must be at most 10 characters long')
    .regex(/^[A-Z0-9-]{2,10}$/, 'Code must contain only uppercase letters, numbers, and hyphens')
    .optional(),
  name: z.string().min(3, 'Name must be at least 3 characters long').optional(),
  description: z.string().optional().nullable(),
  isActive: z.boolean().optional(),
  manager: z.string().optional().nullable(),
  department: z.string().optional().nullable(),
  parentCostCenter: z.string().optional().nullable(),
  budgetLimit: z.number().min(0, 'Budget limit cannot be negative').optional().nullable(),
  budgetPeriod: z.enum(['monthly', 'quarterly', 'annually']).optional(),
  fiscalYear: z.string().optional().nullable(),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ).optional().nullable(),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ).optional().nullable(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional().nullable()
});

/**
 * GET /api/accounting/cost-centers/[id]
 * Get a cost center by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Connect to database
    await connectToDatabase();

    // Get cost center
    const costCenter = await costCenterService.findById(
      id,
      // Type assertion to handle the populate options
      ['manager', 'department', 'parentCostCenter', 'childCostCenters'] as any
    );

    if (!costCenter) {
      return NextResponse.json(
        { error: 'Cost center not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: costCenter
    });
  } catch (error: unknown) {
    logger.error('Error getting cost center', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/cost-centers/[id]
 * Update a cost center
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = updateCostCenterSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Process dates
    const data = {
      ...validationResult.data,
      startDate: validationResult.data.startDate ? new Date(validationResult.data.startDate) : undefined,
      endDate: validationResult.data.endDate ? new Date(validationResult.data.endDate) : undefined
    };

    // Update cost center
    const costCenter = await costCenterService.updateCostCenter(
      id,
      data,
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Cost center updated successfully',
      data: costCenter
    });
  } catch (error: unknown) {
    logger.error('Error updating cost center', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/cost-centers/[id]
 * Delete a cost center
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Connect to database
    await connectToDatabase();

    // Delete cost center
    await costCenterService.deleteById(id);

    return NextResponse.json({
      success: true,
      message: 'Cost center deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting cost center', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
