// app/api/accounting/cost-centers/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { costCenterService } from '@/lib/services/accounting/cost-center-service';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { z } from 'zod';

// Validation schema for creating cost center
const createCostCenterSchema = z.object({
  code: z.string().min(2, 'Code must be at least 2 characters long').max(10, 'Code must be at most 10 characters long')
    .regex(/^[A-Z0-9-]{2,10}$/, 'Code must contain only uppercase letters, numbers, and hyphens'),
  name: z.string().min(3, 'Name must be at least 3 characters long'),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  manager: z.string().optional(),
  department: z.string().optional(),
  parentCostCenter: z.string().optional(),
  budgetLimit: z.number().min(0, 'Budget limit cannot be negative').optional(),
  budgetPeriod: z.enum(['monthly', 'quarterly', 'annually']).default('annually'),
  fiscalYear: z.string().optional(),
  startDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid start date' }
  ).optional(),
  endDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid end date' }
  ).optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional()
});

/**
 * GET /api/accounting/cost-centers
 * List cost centers
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive');
    const department = searchParams.get('department');
    const manager = searchParams.get('manager');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'code';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const hierarchy = searchParams.get('hierarchy') === 'true';

    // If hierarchy is requested, return the cost center hierarchy
    if (hierarchy) {
      const hierarchyData = await costCenterService.getCostCenterHierarchy();
      return NextResponse.json({
        success: true,
        data: hierarchyData
      });
    }

    // Build query
    const query: Record<string, unknown> = {};

    if (isActive !== null) {
      query.isActive = isActive === 'true';
    }

    if (department) {
      query.department = department;
    }

    if (manager) {
      query.manager = manager;
    }

    if (search) {
      query.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: { [key: string]: 1 | -1 } = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get cost centers
    const result = await costCenterService.paginate(
      query,
      page,
      limit,
      sort,
      ['manager', 'department', 'parentCostCenter']
    );

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting cost centers', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/cost-centers
 * Create a cost center
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = createCostCenterSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Process dates
    const data = {
      ...validationResult.data,
      startDate: validationResult.data.startDate ? new Date(validationResult.data.startDate) : undefined,
      endDate: validationResult.data.endDate ? new Date(validationResult.data.endDate) : undefined,
      createdBy: user.id,
      updatedBy: user.id
    };

    // Create cost center
    const costCenter = await costCenterService.createCostCenter(data);

    return NextResponse.json({
      success: true,
      message: 'Cost center created successfully',
      data: costCenter
    });
  } catch (error: unknown) {
    logger.error('Error creating cost center', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
