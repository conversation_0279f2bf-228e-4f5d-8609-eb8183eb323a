// app/api/accounting/budget/reports/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Budget from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import { budgetPerformanceService } from '@/lib/services/accounting/budget-performance-service';
import mongoose from 'mongoose';
// import * as path from 'path';
// import * as fs from 'fs';

// Type definitions to help with MongoDB objects
interface MongoDocument {
  _id?: mongoose.Types.ObjectId;
  [key: string]: unknown;
}

// Budget-specific interfaces
interface BudgetDocument extends MongoDocument {
  name?: string;
  fiscalYear?: string;
  startDate?: Date;
  endDate?: Date;
  categories?: CategoryDocument[];
}

interface CategoryDocument extends MongoDocument {
  name?: string;
  type?: string;
  budgetedAmount?: number;
  total?: number;
  actualAmount?: number;
  subcategories?: SubcategoryDocument[];
  items?: BudgetItemDocument[];
}

interface SubcategoryDocument extends MongoDocument {
  name?: string;
  items?: BudgetItemDocument[];
}

interface BudgetItemDocument extends MongoDocument {
  name?: string;
  description?: string;
  quantity?: number;
  frequency?: number;
  unitCost?: number;
  amount?: number;
}

// MongoDB query type
interface MongoQuery {
  [key: string]: unknown;
}

// MongoDB date query type
interface MongoDateQuery {
  $gte?: Date;
  $lte?: Date;
}

// MongoDB populate options type
interface PopulateOptions {
  path: string;
  populate?: PopulateOptions;
}
import { v4 as uuidv4 } from 'uuid';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/budget/reports
 *
 * Generate budget reports
 */
export async function GET(request: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get('type') || 'budget-summary';
    // Convert null to undefined for budgetId and fiscalYear
    const budgetId = searchParams.get('budgetId') || undefined;
    const fiscalYear = searchParams.get('fiscalYear') || undefined;
    // Parse date strings to Date objects if provided
    let startDate: Date | undefined = undefined;
    let endDate: Date | undefined = undefined;

    try {
      if (searchParams.get('startDate')) {
        startDate = new Date(searchParams.get('startDate') as string);
      }
      if (searchParams.get('endDate')) {
        endDate = new Date(searchParams.get('endDate') as string);
      }
    } catch (error) {
      logger.error('Error parsing date parameters', error);
      // Continue with undefined dates if parsing fails
    }
    const includeCategories = searchParams.get('includeCategories') === 'true';
    const includeSubcategories = searchParams.get('includeSubcategories') === 'true';
    const includeItems = searchParams.get('includeItems') === 'true';
    // We'll use includeTransactions in the future for transaction reports
    // const includeTransactions = searchParams.get('includeTransactions') === 'true';
    const format = searchParams.get('format') || 'pdf';

    // Validate parameters
    if (budgetId && !mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json(
        { error: 'Invalid budget ID' },
        { status: 400 }
      );
    }

    // Generate report data based on report type
    let reportData;

    switch (reportType) {
      case 'budget-summary':
        reportData = await generateBudgetSummaryReport(
          budgetId,
          fiscalYear,
          includeCategories,
          includeSubcategories,
          includeItems
        );
        break;
      case 'budget-performance':
        reportData = await generateBudgetPerformanceReport(
          budgetId,
          fiscalYear
        );
        break;
      case 'budget-variance':
        reportData = await generateBudgetVarianceReport(
          budgetId,
          fiscalYear
        );
        break;
      case 'budget-transactions':
        reportData = await generateBudgetTransactionsReport(
          budgetId,
          startDate,
          endDate
        );
        break;
      case 'budget-categories':
        reportData = await generateBudgetCategoriesReport(
          budgetId,
          fiscalYear,
          includeSubcategories,
          includeItems
        );
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid report type' },
          { status: 400 }
        );
    }

    // Generate report file
    const reportFileName = `budget-report-${uuidv4()}.${format}`;
    const reportUrl = `/api/accounting/budget/reports/download/${reportFileName}`;

    // Store report data in session for download
    // In a real implementation, you would generate the actual file here
    // and store it in a temporary location or in a cloud storage
    console.log(`Generated report with data:`, reportData);

    // For now, we'll just return the report URL
    return NextResponse.json({
      success: true,
      reportUrl,
      reportType,
      reportFormat: format,
      reportData // Include report data in response for debugging
    });
  } catch (error: unknown) {
    logger.error('Error generating budget report', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * Generate budget summary report
 */
async function generateBudgetSummaryReport(
  budgetId?: string | undefined,
  fiscalYear?: string | undefined,
  includeCategories: boolean = true,
  includeSubcategories: boolean = false,
  includeItems: boolean = false
) {
  // Build query
  const query: Record<string, unknown> = {};
  if (budgetId) query._id = new mongoose.Types.ObjectId(budgetId);
  if (fiscalYear) query.fiscalYear = fiscalYear;

  // Get budgets
  let budgetQuery = Budget.find(query);

  // Use the PopulateOptions interface defined earlier

  // Add populate options with proper typing to handle conditional population
  if (includeCategories) {
    const populateOptions: PopulateOptions = {
      path: 'categories'
    };

    if (includeSubcategories) {
      populateOptions.populate = {
        path: 'subcategories'
      };

      if (includeItems) {
        populateOptions.populate.populate = {
          path: 'items'
        };
      }
    }

    budgetQuery = budgetQuery.populate(populateOptions);
  }

  const budgets = await budgetQuery.lean();

  // Return report data
  return {
    title: 'Budget Summary Report',
    generatedAt: new Date(),
    fiscalYear,
    budgets
  };
}

/**
 * Generate budget performance report
 */
async function generateBudgetPerformanceReport(
  budgetId?: string | undefined,
  fiscalYear?: string | undefined
) {
  // Build query
  const query: Record<string, unknown> = {};
  if (budgetId) query._id = new mongoose.Types.ObjectId(budgetId);
  if (fiscalYear) query.fiscalYear = fiscalYear;

  // Get budgets
  const budgets = await Budget.find(query).lean();

  // Get performance data for each budget
  const budgetsWithPerformance = [];
  for (const budget of budgets) {
    try {
      // Safely access budget._id with optional chaining and type guard
      const budgetId = budget?._id?.toString() || 'unknown';
      const performance = await budgetPerformanceService.getBudgetPerformance(budgetId);
      budgetsWithPerformance.push({
        ...budget,
        performance
      });
    } catch (error: unknown) {
      logger.error(`Error getting budget performance for budget ${budget?._id?.toString() || 'unknown'}`, error);
      // Continue with other budgets even if one fails
      budgetsWithPerformance.push(budget);
    }
  }

  // Return report data
  return {
    title: 'Budget Performance Report',
    generatedAt: new Date(),
    fiscalYear,
    budgets: budgetsWithPerformance
  };
}

/**
 * Generate budget variance report
 */
async function generateBudgetVarianceReport(
  budgetId?: string | undefined,
  fiscalYear?: string | undefined
) {
  // Similar to performance report but with focus on variances
  const performanceReport = await generateBudgetPerformanceReport(budgetId, fiscalYear);

  return {
    ...performanceReport,
    title: 'Budget Variance Report'
  };
}

/**
 * Generate budget transactions report
 */
async function generateBudgetTransactionsReport(
  budgetId?: string  | undefined,
  startDate?: Date,
  endDate?: Date
) {
  // Build query
  const query: Record<string, unknown> = {};
  if (budgetId) query.budget = new mongoose.Types.ObjectId(budgetId);

  // Add date range if provided
  if (startDate || endDate) {
    // Use strongly typed interface for MongoDB date query
    const dateQuery: MongoDateQuery = {};
    if (startDate) dateQuery.$gte = startDate;
    if (endDate) dateQuery.$lte = endDate;
    query.date = dateQuery;
  }

  // Get income transactions
  const incomeTransactions = await Income.find({
    ...query,
    status: 'received'
  })
    .populate('budget')
    .populate('budgetCategory')
    .populate('budgetSubcategory')
    .sort({ date: -1 })
    .lean();

  // Get expense transactions
  const expenseTransactions = await Expense.find({
    ...query,
    status: 'paid'
  })
    .populate('budget')
    .populate('budgetCategory')
    .populate('budgetSubcategory')
    .sort({ date: -1 })
    .lean();

  // Return report data
  return {
    title: 'Budget Transactions Report',
    generatedAt: new Date(),
    dateRange: {
      startDate,
      endDate
    },
    incomeTransactions,
    expenseTransactions
  };
}

/**
 * Generate budget categories report
 */
async function generateBudgetCategoriesReport(
  budgetId?: string | undefined,
  fiscalYear?: string | undefined,
  includeSubcategories: boolean = true,
  includeItems: boolean = false
) {
  // Build query
  const query: Record<string, unknown> = {};
  if (budgetId) query._id = new mongoose.Types.ObjectId(budgetId);
  if (fiscalYear) query.fiscalYear = fiscalYear;

  // Get budgets with categories
  let budgetQuery = Budget.find(query);

  // Reuse the PopulateOptions interface defined earlier

  // Add populate options with proper typing to handle conditional population
  const populateOptions: PopulateOptions = {
    path: 'categories'
  };

  if (includeSubcategories) {
    populateOptions.populate = {
      path: 'subcategories'
    };

    if (includeItems) {
      populateOptions.populate.populate = {
        path: 'items'
      };
    }
  }

  budgetQuery = budgetQuery.populate(populateOptions);
  const budgets = await budgetQuery.lean();

  // Return report data
  return {
    title: 'Budget Categories Report',
    generatedAt: new Date(),
    fiscalYear,
    budgets
  };
}
