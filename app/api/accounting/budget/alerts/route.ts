// app/api/accounting/budget/alerts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { budgetPerformanceService } from '@/lib/services/accounting/budget-performance-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Budget from '@/models/accounting/Budget';

/**
 * GET /api/accounting/budget/alerts
 *
 * Get budget alerts based on threshold
 */
export async function GET(request: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const threshold = searchParams.get('threshold') ? parseInt(searchParams.get('threshold') as string) : 90;

    // If budget ID is provided, check that specific budget
    if (budgetId) {
      const alertData = await budgetPerformanceService.checkBudgetThreshold(budgetId, threshold);

      return NextResponse.json({
        success: true,
        alerts: alertData
      });
    }

    // Otherwise, check all active budgets
    const activeBudgets = await Budget.find({ status: 'active' }).select('_id name').lean();

    // Check threshold for each budget
    const alerts = [];
    for (const budget of activeBudgets) {
      try {
        // Type assertion and null check for budget._id
        const budgetId = budget && budget._id ? (budget._id as any).toString() : 'unknown';
        const alertData = await budgetPerformanceService.checkBudgetThreshold(budgetId, threshold);
        if (alertData.exceeded) {
          alerts.push(alertData);
        }
      } catch (error) {
        logger.error(`Error checking budget threshold for budget ${budgetId}`, error);
        // Continue with other budgets even if one fails
      }
    }

    // Return alerts
    return NextResponse.json({
      success: true,
      alerts
    });
  } catch (error: unknown) {
    logger.error('Error getting budget alerts', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
