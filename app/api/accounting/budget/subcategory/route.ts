// app/api/accounting/budget/subcategory/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';
import mongoose from 'mongoose';
import BudgetService from '@/lib/services/accounting/budget-service';
import { BudgetSubcategory } from '@/models/accounting/Budget';

export const runtime = 'nodejs';



// Create a budget service instance
const budgetService = new BudgetService();

// Budget subcategory validation schema
const subcategorySchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().optional(),
  parentCategory: z.string().min(1, 'Parent category ID is required'),
  budget: z.string().min(1, 'Budget ID is required'),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const categoryId = searchParams.get('categoryId');

    if (!budgetId) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }

    // Use the imported BudgetSubcategory model

    // Build filter
    const filter: Record<string, any> = { budget: budgetId };
    if (categoryId) {
      filter.parentCategory = categoryId;
    }

    // Get subcategories
    const subcategories = await BudgetSubcategory.find(filter)
      .sort({ name: 1 })
      .select('id name description parentCategory budget')
      .lean();

    // Return subcategories
    return NextResponse.json({ subcategories });
  } catch (error: unknown) {
    logger.error('Error fetching budget subcategories', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = subcategorySchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Create subcategory
    const subcategory = await budgetService.createBudgetSubcategory(validationResult.data);

    // Return created subcategory
    return NextResponse.json({
      success: true,
      message: 'Budget subcategory created successfully',
      subcategory
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating budget subcategory', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
