// app/api/accounting/budget/export/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import * as XLSX from 'xlsx';
import BudgetService from '@/lib/services/accounting/budget-service';

// Create a budget service instance
const budgetService = new BudgetService();

/**
 * GET /api/accounting/budget/export
 * Export budget data to Excel or CSV
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const format = searchParams.get('format') || 'xlsx';
    const type = searchParams.get('type') || 'all';

    if (!id) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }

    // Get budget with details
    const budget = await budgetService.getBudgetWithDetails(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Prepare data for export
    const exportData: unknown[] = [];

    // Process categories
    const budgetData = budget as any; // Type assertion to avoid TypeScript errors
    if (budgetData.categories && Array.isArray(budgetData.categories)) {
      for (const category of budgetData.categories) {
        // Skip category if filtering by type or if category doesn't have required properties
        if (!category || typeof category !== 'object') continue;

        // Use type assertion to access properties
        const categoryObj = category as any;
        if (type !== 'all' && categoryObj.type !== type) {
          continue;
        }

        const categoryName = typeof categoryObj.name === 'string' ? categoryObj.name : 'Unknown';
        const categoryType = typeof categoryObj.type === 'string' ? categoryObj.type : 'Unknown';

        // Process subcategories
        if (categoryObj.subcategories && Array.isArray(categoryObj.subcategories)) {
          for (const subcategory of categoryObj.subcategories) {
            if (!subcategory || typeof subcategory !== 'object') continue;

            // Use type assertion to access properties
            const subcategoryObj = subcategory as any;
            const subcategoryName = typeof subcategoryObj.name === 'string' ? subcategoryObj.name : 'Unknown';

            // Process items in subcategory
            if (subcategoryObj.items && Array.isArray(subcategoryObj.items)) {
              for (const item of subcategoryObj.items) {
                if (!item || typeof item !== 'object') continue;

                // Use type assertion to access properties
                const itemObj = item as any;
                exportData.push({
                  CategoryName: categoryName,
                  CategoryType: categoryType,
                  SubcategoryName: subcategoryName,
                  Name: typeof itemObj.name === 'string' ? itemObj.name : 'Unknown',
                  Description: typeof itemObj.description === 'string' ? itemObj.description : '',
                  Quantity: typeof itemObj.quantity === 'number' ? itemObj.quantity : 0,
                  Frequency: typeof itemObj.frequency === 'number' ? itemObj.frequency : 0,
                  UnitCost: typeof itemObj.unitCost === 'number' ? itemObj.unitCost : 0,
                  Amount: typeof itemObj.amount === 'number' ? itemObj.amount : 0,
                });
              }
            }
          }
        }

        // Process direct items in category
        if (categoryObj.items && Array.isArray(categoryObj.items)) {
          for (const item of categoryObj.items) {
            if (!item || typeof item !== 'object') continue;

            // Use type assertion to access properties
            const itemObj = item as any;
            exportData.push({
              CategoryName: categoryName,
              CategoryType: categoryType,
              SubcategoryName: '',
              Name: typeof itemObj.name === 'string' ? itemObj.name : 'Unknown',
              Description: typeof itemObj.description === 'string' ? itemObj.description : '',
              Quantity: typeof itemObj.quantity === 'number' ? itemObj.quantity : 0,
              Frequency: typeof itemObj.frequency === 'number' ? itemObj.frequency : 0,
              UnitCost: typeof itemObj.unitCost === 'number' ? itemObj.unitCost : 0,
              Amount: typeof itemObj.amount === 'number' ? itemObj.amount : 0,
            });
          }
        }
      }
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Budget Items');

    // Generate buffer
    let buffer: Buffer;
    let contentType: string;
    let filename: string;

    if (format === 'csv') {
      // Convert to CSV
      const csvContent = XLSX.utils.sheet_to_csv(worksheet);
      buffer = Buffer.from(csvContent);
      contentType = 'text/csv';
      filename = `budget-${budget.name.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.csv`;
    } else {
      // Generate Excel buffer
      buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      filename = `budget-${budget.name.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.xlsx`;
    }

    // Log export
    logger.info('Budget data exported', {
      userId: user.id,
      budgetId: id,
      format,
      itemCount: exportData.length
    });

    // Return file
    return new NextResponse(buffer as unknown as BodyInit, {
      headers: {
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Type': contentType
      }
    });
  } catch (error: unknown) {
    logger.error('Error exporting budget data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
