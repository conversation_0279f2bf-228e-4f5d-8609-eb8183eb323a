// app/api/accounting/budget/project/[id]/performance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import projectBudgetIntegrationService from '@/lib/services/accounting/project-budget-integration-service';

/**
 * GET /api/accounting/budget/project/[id]/performance
 * Get project budget performance
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  // Declare projectId at function scope with default value
  let projectId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROJECT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;
    projectId = id;

    // Validate project ID
    if (!id) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Get project budget performance
    const performance = await projectBudgetIntegrationService.getProjectBudgetPerformance(projectId);

    // Log the request
    logger.info('Project budget performance retrieved', {
      userId: user.id,
      projectId: projectId
    });

    return NextResponse.json({
      success: true,
      performance
    });
  } catch (error) {
    logger.error(`Error getting project budget performance: ${projectId}`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
