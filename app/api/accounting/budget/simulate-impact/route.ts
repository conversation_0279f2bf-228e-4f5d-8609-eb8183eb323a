// app/api/accounting/budget/simulate-impact/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';
import { z } from 'zod';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import BudgetSubcategory from '@/models/accounting/BudgetSubcategory';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';

// Type definitions to help with MongoDB objects
interface MongoDocument {
  _id?: mongoose.Types.ObjectId;
  [key: string]: unknown;
}

// Budget-specific interfaces
interface BudgetDocument extends MongoDocument {
  name?: string;
  fiscalYear?: string;
  categories?: CategoryDocument[];
}

interface CategoryDocument extends MongoDocument {
  name?: string;
  type?: string;
  budget?: mongoose.Types.ObjectId;
  parentCategory?: mongoose.Types.ObjectId;
  subcategories?: SubcategoryDocument[];
  items?: BudgetItemDocument[];
}

interface SubcategoryDocument extends MongoDocument {
  name?: string;
  parentCategory?: mongoose.Types.ObjectId;
  items?: BudgetItemDocument[];
}

interface BudgetItemDocument extends MongoDocument {
  name?: string;
  description?: string;
  quantity?: number;
  frequency?: number;
  unitCost?: number;
  amount?: number;
}

// Simulation result interfaces
interface SimulationResult {
  budget: {
    id: string;
    name: string;
    fiscalYear: string;
  };
  originalTotal: number;
  newTotal: number;
  difference: number;
  percentageChange: number;
  categories: CategorySimulationResult[];
}

interface CategorySimulationResult {
  id: string;
  name: string;
  originalTotal: number;
  newTotal: number;
  difference: number;
  percentageChange: number;
}

export const runtime = 'nodejs';



// Define the transaction schema for validation
const transactionSchema = z.object({
  type: z.enum(['income', 'expense']),
  amount: z.number().positive("Amount must be positive"),
  budgetId: z.string().min(1, "Budget ID is required"),
  categoryId: z.string().min(1, "Category ID is required"),
  subcategoryId: z.string().optional(),
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }).optional(),
});

// Define the simulation request schema
const simulationRequestSchema = z.object({
  transactions: z.array(transactionSchema).min(1, "At least one transaction is required"),
  includeExisting: z.boolean().default(true),
});

/**
 * POST handler for simulating budget impact of transactions
 * @param req The request object
 * @returns A response with the simulated budget impact
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = simulationRequestSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { transactions, includeExisting } = validationResult.data;

    // Group transactions by budget
    const transactionsByBudget = transactions.reduce((acc, transaction) => {
      if (!acc[transaction.budgetId]) {
        acc[transaction.budgetId] = [];
      }
      acc[transaction.budgetId].push(transaction);
      return acc;
    }, {} as Record<string, typeof transactions>);

    // Process each budget
    const simulationResults = [];
    for (const [budgetId, budgetTransactions] of Object.entries(transactionsByBudget)) {
      // Fetch the budget
      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        return NextResponse.json(
          { error: `Budget with ID ${budgetId} not found` },
          { status: 404 }
        );
      }

      // Fetch budget categories
      const categories = await BudgetCategory.find({
        budget: new mongoose.Types.ObjectId(budgetId)
      }).lean();

      // Create a map of categories by ID for quick lookup
      const categoriesById = categories.reduce((acc, category) => {
        // Safely access category._id with optional chaining
        const categoryId = category?._id?.toString() || 'unknown';
        acc[categoryId] = category as CategoryDocument;
        return acc;
      }, {} as Record<string, CategoryDocument>);

      // Fetch subcategories if needed
      const subcategoryIds = budgetTransactions
        .filter(t => t.subcategoryId)
        .map(t => t.subcategoryId);

      let subcategoriesById = {} as Record<string, any>;
      if (subcategoryIds.length > 0) {
        const subcategories = await BudgetSubcategory.find({
          _id: { $in: subcategoryIds.map(id => new mongoose.Types.ObjectId(id)) }
        }).lean();

        subcategoriesById = subcategories.reduce((acc, subcategory) => {
          // Safely access subcategory._id with optional chaining
          const subcategoryId = subcategory?._id?.toString() || 'unknown';
          acc[subcategoryId] = subcategory as SubcategoryDocument;
          return acc;
        }, {} as Record<string, SubcategoryDocument>);
      }

      // Get current actual amounts if including existing transactions
      let currentIncomeByCategory = {} as Record<string, number>;
      let currentExpenseByCategory = {} as Record<string, number>;

      if (includeExisting) {
        // Get current income by category
        const incomeAggregation = await Income.aggregate([
          {
            $match: {
              budget: new mongoose.Types.ObjectId(budgetId),
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: '$budgetCategory',
              actual: { $sum: '$amount' }
            }
          }
        ]);

        currentIncomeByCategory = incomeAggregation.reduce((acc, item) => {
          acc[item._id.toString()] = item.actual;
          return acc;
        }, {} as Record<string, number>);

        // Get current expense by category
        const expenseAggregation = await Expense.aggregate([
          {
            $match: {
              budget: new mongoose.Types.ObjectId(budgetId),
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: '$budgetCategory',
              actual: { $sum: '$amount' }
            }
          }
        ]);

        currentExpenseByCategory = expenseAggregation.reduce((acc, item) => {
          acc[item._id.toString()] = item.actual;
          return acc;
        }, {} as Record<string, number>);
      }

      // Calculate simulated impact
      const simulatedCategories = [];
      const processedCategoryIds = new Set();

      for (const transaction of budgetTransactions) {
        const { type, amount, categoryId, subcategoryId } = transaction;
        const category = categoriesById[categoryId];

        if (!category) {
          return NextResponse.json(
            { error: `Category with ID ${categoryId} not found` },
            { status: 404 }
          );
        }

        // Verify category belongs to budget and matches transaction type
        const categoryObj = category as CategoryDocument;
        if (!categoryObj.budget || categoryObj.budget.toString() !== budgetId || categoryObj.type !== type) {
          return NextResponse.json(
            { error: `Category ${categoryObj.name || 'Unknown'} is not a ${type} category for this budget` },
            { status: 400 }
          );
        }

        // Verify subcategory if provided
        if (subcategoryId) {
          const subcategory = subcategoriesById[subcategoryId];
          if (!subcategory) {
            return NextResponse.json(
              { error: `Subcategory with ID ${subcategoryId} not found` },
              { status: 404 }
            );
          }

          const subcategoryObj = subcategory as SubcategoryDocument;
          if (!subcategoryObj.parentCategory || subcategoryObj.parentCategory.toString() !== categoryId) {
            return NextResponse.json(
              { error: `Subcategory ${subcategoryObj.name || 'Unknown'} does not belong to category ${categoryObj.name || 'Unknown'}` },
              { status: 400 }
            );
          }
        }

        // Calculate current actual amount
        const currentActual = type === 'income'
          ? currentIncomeByCategory[categoryId] || 0
          : currentExpenseByCategory[categoryId] || 0;

        // Calculate simulated amount
        const simulatedActual = currentActual + amount;
        const budgeted = (categoryObj.budgetedAmount as number) || 0;
        const remaining = budgeted - simulatedActual;
        const utilizationPercentage = budgeted > 0 ? (simulatedActual / budgeted) * 100 : 0;

        // Add to processed categories if not already processed
        if (!processedCategoryIds.has(categoryId)) {
          simulatedCategories.push({
            id: categoryId,
            name: category.name,
            type: category.type,
            budgeted,
            currentActual,
            simulatedActual,
            simulatedRemaining: remaining,
            simulatedUtilizationPercentage: utilizationPercentage,
            impact: amount,
            impactPercentage: budgeted > 0 ? (amount / budgeted) * 100 : 0,
            status: type === 'income'
              ? (utilizationPercentage >= 100 ? 'achieved' : 'under_target')
              : (utilizationPercentage > 100 ? 'over_budget' : 'within_budget')
          });

          processedCategoryIds.add(categoryId);
        } else {
          // Update existing category in the results
          const categoryIndex = simulatedCategories.findIndex(c => c.id === categoryId);
          if (categoryIndex !== -1) {
            const existingCategory = simulatedCategories[categoryIndex];
            existingCategory.simulatedActual += amount;
            existingCategory.simulatedRemaining = budgeted - existingCategory.simulatedActual;
            existingCategory.simulatedUtilizationPercentage = budgeted > 0
              ? (existingCategory.simulatedActual / budgeted) * 100
              : 0;
            existingCategory.impact += amount;
            existingCategory.impactPercentage = budgeted > 0
              ? (existingCategory.impact / budgeted) * 100
              : 0;
            existingCategory.status = type === 'income'
              ? (existingCategory.simulatedUtilizationPercentage >= 100 ? 'achieved' : 'under_target')
              : (existingCategory.simulatedUtilizationPercentage > 100 ? 'over_budget' : 'within_budget');
          }
        }
      }

      // Calculate overall budget impact
      const totalBudgetedIncome = categories
        .filter(cat => cat.type === 'income')
        .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);

      const totalBudgetedExpense = categories
        .filter(cat => cat.type === 'expense')
        .reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);

      const totalCurrentIncome = Object.values(currentIncomeByCategory).reduce((sum, amount) => sum + amount, 0);
      const totalCurrentExpense = Object.values(currentExpenseByCategory).reduce((sum, amount) => sum + amount, 0);

      const totalSimulatedIncome = totalCurrentIncome + budgetTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);

      const totalSimulatedExpense = totalCurrentExpense + budgetTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      // Add budget simulation result with proper typing
      const budgetObj = budget as MongoDocument;
      simulationResults.push({
        budget: {
          id: budgetObj._id?.toString() || 'unknown',
          name: (budgetObj.name as string) || 'Unknown',
          fiscalYear: (budgetObj.fiscalYear as string) || 'Unknown'
        },
        overall: {
          income: {
            budgeted: totalBudgetedIncome,
            currentActual: totalCurrentIncome,
            simulatedActual: totalSimulatedIncome,
            impact: totalSimulatedIncome - totalCurrentIncome,
            currentUtilizationPercentage: totalBudgetedIncome > 0
              ? (totalCurrentIncome / totalBudgetedIncome) * 100
              : 0,
            simulatedUtilizationPercentage: totalBudgetedIncome > 0
              ? (totalSimulatedIncome / totalBudgetedIncome) * 100
              : 0
          },
          expense: {
            budgeted: totalBudgetedExpense,
            currentActual: totalCurrentExpense,
            simulatedActual: totalSimulatedExpense,
            impact: totalSimulatedExpense - totalCurrentExpense,
            currentUtilizationPercentage: totalBudgetedExpense > 0
              ? (totalCurrentExpense / totalBudgetedExpense) * 100
              : 0,
            simulatedUtilizationPercentage: totalBudgetedExpense > 0
              ? (totalSimulatedExpense / totalBudgetedExpense) * 100
              : 0
          }
        },
        categories: simulatedCategories
      });
    }

    // Return the simulation results
    return NextResponse.json({
      success: true,
      simulationResults
    });
  } catch (error: unknown) {
    logger.error('Error simulating budget impact', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
