// app/api/accounting/budget/verify-integration/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';

import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import { budgetIncomeIntegrationService } from '@/lib/services/accounting/budget-income-integration';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * API to verify budget-income integration
 * This endpoint helps test that income items are properly saved to budget categories
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const budgetId = searchParams.get('budgetId');
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';

    let targetBudget;

    if (budgetId) {
      // Verify specific budget
      targetBudget = await Budget.findById(budgetId);
      if (!targetBudget) {
        return NextResponse.json(
          { error: `Budget ${budgetId} not found` },
          { status: 404 }
        );
      }
    } else {
      // Find active budget for fiscal year
      targetBudget = await Budget.findOne({
        fiscalYear,
        status: { $in: ['active', 'approved'] }
      });

      if (!targetBudget) {
        return NextResponse.json(
          { error: `No active budget found for fiscal year ${fiscalYear}` },
          { status: 404 }
        );
      }
    }

    // Get verification data
    const verification = await budgetIncomeIntegrationService.verifyBudgetCategoryItemsIntegration(
      targetBudget._id.toString()
    );

    // Get related income records
    const incomeRecords = await Income.find({
      budget: targetBudget._id,
      appliedToBudget: true
    }).select('_id reference amount status source date budgetCategory');

    // Get budget categories with items
    const categories = await BudgetCategory.find({
      budget: targetBudget._id
    }).lean();

    // Calculate summary statistics
    const summary = {
      budgetInfo: {
        id: targetBudget._id,
        name: targetBudget.name,
        fiscalYear: targetBudget.fiscalYear,
        status: targetBudget.status,
        totalIncome: targetBudget.totalIncome,
        totalActualIncome: targetBudget.totalActualIncome
      },
      incomeRecords: {
        total: incomeRecords.length,
        byStatus: incomeRecords.reduce((acc: any, income) => {
          acc[income.status] = (acc[income.status] || 0) + 1;
          return acc;
        }, {}),
        totalAmount: incomeRecords.reduce((sum, income) => sum + income.amount, 0)
      },
      categories: categories.map(cat => ({
        id: cat._id,
        name: cat.name,
        type: cat.type,
        actualAmount: cat.actualAmount,
        budgetedAmount: cat.budgetedAmount,
        itemsCount: cat.items ? cat.items.length : 0,
        items: cat.items || []
      })),
      integration: {
        categoriesWithItems: categories.filter(cat => cat.items && cat.items.length > 0).length,
        totalCategoryItems: categories.reduce((sum, cat) => (cat.items ? cat.items.length : 0) + sum, 0),
        incomeLinkedToCategories: incomeRecords.filter(income => 
          categories.some(cat => 
            cat.items && cat.items.some((item: any) => 
              item.sourceId && item.sourceId.toString() === income._id.toString()
            )
          )
        ).length
      }
    };

    logger.info('Budget integration verification completed', {
      budgetId: targetBudget._id,
      fiscalYear: targetBudget.fiscalYear,
      incomeCount: incomeRecords.length,
      categoriesCount: categories.length,
      integrationStatus: summary.integration
    });

    return NextResponse.json({
      success: true,
      message: 'Budget integration verification completed',
      verification,
      summary,
      detailedData: {
        incomeRecords,
        categories
      }
    });

  } catch (error: unknown) {
    logger.error('Error verifying budget integration', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST method to manually trigger budget integration for existing income
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Only super admin can trigger manual integration' },
        { status: 403 }
      );
    }

    const data = await req.json();
    const { incomeId, action } = data;

    if (!incomeId || !action) {
      return NextResponse.json(
        { error: 'incomeId and action are required' },
        { status: 400 }
      );
    }

    // Find the income record
    const income = await Income.findById(incomeId);
    if (!income) {
      return NextResponse.json(
        { error: 'Income record not found' },
        { status: 404 }
      );
    }

    let result;
    switch (action) {
      case 'create':
        await budgetIncomeIntegrationService.createIncomeAsBudgetItem(income);
        result = 'BudgetItem created for income';
        break;
      case 'update':
        await budgetIncomeIntegrationService.updateIncomeAsBudgetItem(income);
        result = 'BudgetItem updated for income';
        break;
      case 'remove':
        await budgetIncomeIntegrationService.removeIncomeAsBudgetItem(income);
        result = 'BudgetItem removed for income';
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: create, update, or remove' },
          { status: 400 }
        );
    }

    logger.info('Manual budget integration triggered', {
      incomeId,
      action,
      triggeredBy: user.id
    });

    return NextResponse.json({
      success: true,
      message: result,
      incomeId,
      action
    });

  } catch (error: unknown) {
    logger.error('Error in manual budget integration', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
