// app/api/accounting/budget/categories/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger, LogCategory } from '@/lib/backend/utils/logger';
import Budget from '@/models/accounting/Budget';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { BudgetCategory, ExpenditureApiResponse } from '@/types/expenditure';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/budget/categories
 * Get budget categories for a specific budget and type
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const budgetId = searchParams.get('budgetId');
    const type = searchParams.get('type') || 'expense'; // 'income' or 'expense'
    const fiscalYear = searchParams.get('fiscalYear');

    logger.info('Fetching budget categories', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetId,
      type,
      fiscalYear
    });

    let categories: BudgetCategory[] = [];

    if (budgetId) {
      // Get categories from specific budget
      const budget = await Budget.findById(budgetId).lean();

      if (!budget) {
        return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
      }

      // Filter categories by type
      const rawCategories = budget.categories || [];
      categories = rawCategories
        .filter((category) => {
          if (type === 'expense') {
            return category.type === 'expenditure' || category.type === 'expense';
          } else if (type === 'income') {
            return category.type === 'income' || category.type === 'revenue';
          }
          return true;
        })
        .map((category): BudgetCategory => ({
          id: category.id || category._id?.toString() || '',
          _id: category._id?.toString(),
          name: category.name || '',
          description: category.description,
          type: category.type as 'income' | 'expense' | 'expenditure' | 'revenue',
          budgetId: budgetId,
          budgetedAmount: category.budgetedAmount || 0,
          actualAmount: category.actualAmount || 0,
          isActive: category.isActive !== false,
          createdAt: category.createdAt,
          updatedAt: category.updatedAt
        }));

    } else {
      // Get categories from all active budgets
      interface BudgetQuery {
        status: string;
        fiscalYear?: string;
      }

      const query: BudgetQuery = { status: 'active' };
      if (fiscalYear) {
        query.fiscalYear = fiscalYear;
      }

      const budgets = await Budget.find(query).lean();

      // Collect all categories from all budgets
      const allCategories: BudgetCategory[] = [];
      budgets.forEach(budget => {
        if (budget.categories) {
          budget.categories.forEach((category) => {
            const shouldInclude = type === 'expense'
              ? (category.type === 'expenditure' || category.type === 'expense')
              : type === 'income'
              ? (category.type === 'income' || category.type === 'revenue')
              : true;

            if (shouldInclude) {
              allCategories.push({
                id: category.id || category._id?.toString() || '',
                _id: category._id?.toString(),
                name: category.name || '',
                description: category.description,
                type: category.type as 'income' | 'expense' | 'expenditure' | 'revenue',
                budgetId: budget._id?.toString(),
                budgetName: budget.name,
                budgetedAmount: category.budgetedAmount || 0,
                actualAmount: category.actualAmount || 0,
                isActive: category.isActive !== false,
                createdAt: category.createdAt,
                updatedAt: category.updatedAt
              });
            }
          });
        }
      });

      // Remove duplicates by name and sort
      const uniqueCategories = allCategories.reduce((acc: BudgetCategory[], category: BudgetCategory) => {
        const existing = acc.find((c) => c.name === category.name);
        if (!existing) {
          acc.push(category);
        }
        return acc;
      }, []);

      categories = uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));
    }

    // Format categories for frontend consumption
    const formattedCategories: BudgetCategory[] = categories.map(category => ({
      ...category,
      variance: category.actualAmount - category.budgetedAmount,
      utilizationPercentage: category.budgetedAmount > 0
        ? (category.actualAmount / category.budgetedAmount) * 100
        : 0,
      budgetId: category.budgetId || budgetId || '',
    }));

    logger.info('Budget categories retrieved successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetId,
      type,
      categoriesCount: formattedCategories.length
    });

    const response: ExpenditureApiResponse = {
      categories: formattedCategories,
      total: formattedCategories.length,
      budgetId,
      type,
      fiscalYear
    };

    return NextResponse.json(response);

  } catch (error: unknown) {
    logger.error('Error fetching budget categories', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
