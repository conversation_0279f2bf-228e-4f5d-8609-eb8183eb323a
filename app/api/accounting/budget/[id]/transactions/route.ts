// app/api/accounting/budget/[id]/transactions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { logger } from '@/lib/utils/logger';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/accounting/budget/[id]/transactions
 * Fetch transactions for a specific budget
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare budgetId at function scope with default value
  let budgetId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    budgetId = id;
    // Get session
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Budget ID is already available from params

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Check if budget exists
    const budget = await Budget.findById(budgetId);
    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type') || 'all';
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : null;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : null;
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const status = searchParams.get('status');

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Build base filters
    const incomeFilters: Record<string, any> = { budget: budgetId };
    const expenseFilters: Record<string, any> = { budget: budgetId };

    // Add date filters if provided
    if (startDate) {
      incomeFilters.date = { $gte: startDate };
      expenseFilters.date = { $gte: startDate };
    }
    if (endDate) {
      incomeFilters.date = { ...incomeFilters.date, $lte: endDate };
      expenseFilters.date = { ...expenseFilters.date, $lte: endDate };
    }

    // Add category filter if provided
    if (categoryId) {
      incomeFilters.budgetCategory = categoryId;
      expenseFilters.budgetCategory = categoryId;
    }

    // Add subcategory filter if provided
    if (subcategoryId) {
      incomeFilters.budgetSubcategory = subcategoryId;
      expenseFilters.budgetSubcategory = subcategoryId;
    }

    // Add status filter if provided
    if (status) {
      incomeFilters.status = status;
      expenseFilters.status = status;
    }

    // Fetch transactions based on type
    interface TransactionItem {
      id: string;
      type: string;
      date: Date;
      amount: number;
      description: string;
      reference?: string;
      status: string;
      budget: { id: string; name: string };
      category: { id: string; name: string; type: string } | null;
      subcategory?: { id: string; name: string } | null;
      source?: string;
      subSource?: string;
      expenseCategory?: string;
      expenseSubcategory?: string;
      fiscalYear?: string;
      createdBy: string;
      createdAt: Date;
    }

    let transactions: TransactionItem[] = [];
    let totalCount = 0;

    if (type === 'income' || type === 'all') {
      // Fetch income transactions
      const incomeTransactions = await Income.find(incomeFilters)
        .sort({ date: -1 })
        .skip(type === 'all' ? 0 : skip)
        .limit(type === 'all' ? 0 : limit)
        .populate('budgetCategory', 'name type')
        .populate('budgetSubcategory', 'name')
        .lean();

      // Count total income transactions
      const incomeCount = await Income.countDocuments(incomeFilters);

      // Format income transactions
      const formattedIncomeTransactions = incomeTransactions.map(income => ({
        id: income._id ? income._id.toString() : '',
        type: 'income',
        date: income.date,
        amount: income.amount,
        description: income.description,
        reference: income.reference,
        status: income.status,
        budget: {
          id: budget._id ? budget._id.toString() : '',
          name: budget.name
        },
        category: income.budgetCategory ? {
          id: income.budgetCategory._id ? income.budgetCategory._id.toString() : '',
          name: income.budgetCategory.name,
          type: 'income'
        } : null,
        subcategory: income.budgetSubcategory ? {
          id: income.budgetSubcategory._id ? income.budgetSubcategory._id.toString() : '',
          name: income.budgetSubcategory.name
        } : null,
        source: income.source,
        subSource: income.subSource,
        fiscalYear: income.fiscalYear,
        createdBy: income.createdBy,
        createdAt: income.createdAt
      }));

      transactions = [...transactions, ...formattedIncomeTransactions];
      totalCount += incomeCount;
    }

    if (type === 'expense' || type === 'all') {
      // Fetch expense transactions
      const expenseTransactions = await Expense.find(expenseFilters)
        .sort({ date: -1 })
        .skip(type === 'all' ? 0 : skip)
        .limit(type === 'all' ? 0 : limit)
        .populate('budgetCategory', 'name type')
        .populate('budgetSubcategory', 'name')
        .lean();

      // Count total expense transactions
      const expenseCount = await Expense.countDocuments(expenseFilters);

      // Format expense transactions
      const formattedExpenseTransactions = expenseTransactions.map(expense => ({
        id: expense._id ? expense._id.toString() : '',
        type: 'expense',
        date: expense.date,
        amount: expense.amount,
        description: expense.description,
        reference: expense.reference,
        status: expense.status,
        budget: {
          id: budget._id ? budget._id.toString() : '',
          name: budget.name
        },
        category: expense.budgetCategory ? {
          id: expense.budgetCategory._id ? expense.budgetCategory._id.toString() : '',
          name: expense.budgetCategory.name,
          type: 'expense'
        } : null,
        subcategory: expense.budgetSubcategory ? {
          id: expense.budgetSubcategory._id ? expense.budgetSubcategory._id.toString() : '',
          name: expense.budgetSubcategory.name
        } : null,
        expenseCategory: expense.category,
        expenseSubcategory: expense.subcategory,
        fiscalYear: expense.fiscalYear,
        createdBy: expense.createdBy,
        createdAt: expense.createdAt
      }));

      transactions = [...transactions, ...formattedExpenseTransactions];
      totalCount += expenseCount;
    }

    // Sort combined transactions by date (newest first)
    transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Apply pagination to combined results if type is 'all'
    if (type === 'all') {
      transactions = transactions.slice(skip, skip + limit);
    }

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limit);

    // Return transactions
    return NextResponse.json({
      transactions,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error) {
    logger.error('Error fetching budget transactions', error);
    return NextResponse.json(
      { error: 'Failed to fetch budget transactions' },
      { status: 500 }
    );
  }
}
