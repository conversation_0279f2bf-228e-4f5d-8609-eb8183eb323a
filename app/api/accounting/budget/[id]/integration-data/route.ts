// app/api/accounting/budget/[id]/integration-data/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid budget ID' },
        { status: 400 }
      );
    }

    // Get budget
    const budget = await Budget.findById(id).lean();
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Get budget categories
    const categories = await BudgetCategory.find({ budget: id }).lean();

    // Separate income and expense categories
    const incomeCategories = categories.filter(cat => cat.type === 'income');
    const expenseCategories = categories.filter(cat => cat.type === 'expense' || cat.type === 'expenditure');

    // Calculate totals for budgeted amounts
    const totalBudgetedIncome = incomeCategories.reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);
    const totalBudgetedExpense = expenseCategories.reduce((sum, cat) => sum + (cat.budgetedAmount || 0), 0);

    // Get actual income data
    const incomeData = await Income.aggregate([
      {
        $match: {
          budget: new mongoose.Types.ObjectId(id),
          status: { $in: ['received', 'approved'] },
          appliedToBudget: true
        }
      },
      {
        $group: {
          _id: '$budgetCategory',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Get actual expenditure data from BudgetExpenditure model
    const expenditureData = await BudgetExpenditure.aggregate([
      {
        $match: {
          budget: new mongoose.Types.ObjectId(id),
          status: { $in: ['approved', 'paid'] },
          appliedToBudget: true
        }
      },
      {
        $group: {
          _id: '$budgetCategory',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Create maps for quick lookup
    const incomeActualMap = new Map();
    incomeData.forEach(item => {
      if (item._id) {
        incomeActualMap.set(item._id.toString(), item.totalAmount);
      }
    });

    const expenseActualMap = new Map();
    expenditureData.forEach(item => {
      if (item._id) {
        expenseActualMap.set(item._id.toString(), item.totalAmount);
      }
    });

    // Calculate totals for actual amounts
    const totalActualIncome = Array.from(incomeActualMap.values()).reduce((sum, amount) => sum + amount, 0);
    const totalActualExpense = Array.from(expenseActualMap.values()).reduce((sum, amount) => sum + amount, 0);

    // Format income categories with actual data
    const formattedIncomeCategories = incomeCategories.map(category => {
      const actual = incomeActualMap.get(category._id.toString()) || 0;
      const budgeted = category.budgetedAmount || 0;
      const variance = actual - budgeted;
      const percentage = budgeted > 0 ? Math.min((actual / budgeted) * 100, 100) : 0;

      return {
        id: category._id.toString(),
        name: category.name,
        budgeted,
        actual,
        variance,
        percentage
      };
    });

    // Format expense categories with actual data
    const formattedExpenseCategories = expenseCategories.map(category => {
      const actual = expenseActualMap.get(category._id.toString()) || 0;
      const budgeted = category.budgetedAmount || 0;
      const variance = actual - budgeted;
      const percentage = budgeted > 0 ? Math.min((actual / budgeted) * 100, 100) : 0;

      return {
        id: category._id.toString(),
        name: category.name,
        budgeted,
        actual,
        variance,
        percentage
      };
    });

    // Prepare response data
    const integrationData = {
      budgetId: budget._id.toString(),
      budgetName: budget.name,
      fiscalYear: budget.fiscalYear,
      totalBudgetedIncome,
      totalBudgetedExpense,
      totalActualIncome,
      totalActualExpense,
      incomeCategories: formattedIncomeCategories,
      expenseCategories: formattedExpenseCategories,
      lastUpdated: new Date()
    };

    return NextResponse.json(integrationData);

  } catch (error: unknown) {
    console.error('Error fetching budget integration data:', error);
    logger.error('Error fetching budget integration data', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// POST method to manually trigger budget actuals update
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid budget ID' },
        { status: 400 }
      );
    }

    // Import budget integration service
    const budgetIntegrationService = (await import('@/lib/services/accounting/budget-integration-service')).default;
    
    // Update budget actuals
    await budgetIntegrationService.updateBudgetActuals(id);

    return NextResponse.json({
      success: true,
      message: 'Budget actuals updated successfully',
      updatedAt: new Date()
    });

  } catch (error: unknown) {
    console.error('Error updating budget actuals:', error);
    logger.error('Error updating budget actuals', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
