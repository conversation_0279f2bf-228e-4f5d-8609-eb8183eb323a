// app/api/accounting/budget/[id]/categories/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { BudgetService } from '@/lib/services/accounting/budget-service';

export const runtime = 'nodejs';



// Create a budget service instance
const budgetService = new BudgetService();

/**
 * GET /api/accounting/budget/[id]/categories
 * Get categories for a specific budget
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type') as 'income' | 'expense' | null;

    // Build filter
    const filter: Record<string, any> = {
      budget: id
    };

    // Add type filter if provided
    if (type && (type === 'income' || type === 'expense')) {
      filter.type = type;
    }

    console.log('Budget categories API - Filter:', filter);
    console.log('Budget categories API - Budget ID:', id);
    console.log('Budget categories API - Type:', type);

    // Get categories from the BudgetCategory collection
    const categories = await BudgetCategory.find(filter)
      .sort({ name: 1 })
      .select('id name description type budget budgetedAmount actualAmount')
      .lean();

    console.log('Budget categories API - Found categories:', categories);

    // Return categories
    return NextResponse.json({
      categories
    });
  } catch (error: unknown) {
    logger.error(`Error getting budget categories`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/accounting/budget/[id]/categories
 * Add a new category to a budget
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.type) {
      return NextResponse.json(
        { error: 'Missing required fields: name and type are required' },
        { status: 400 }
      );
    }

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Create category using the budget service
    const categoryData = {
      name: body.name,
      description: body.description,
      type: body.type,
      budget: id
    };

    const category = await budgetService.createBudgetCategory(categoryData);

    return NextResponse.json({
      success: true,
      message: 'Category added successfully',
      category
    });
  } catch (error: unknown) {
    logger.error(`Error adding budget category`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/accounting/budget/[id]/categories
 * Update multiple categories in a budget
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate request
    if (!body.categoryId || !body.name) {
      return NextResponse.json(
        { error: 'Invalid request: categoryId and name are required' },
        { status: 400 }
      );
    }

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Find the category
    const category = await BudgetCategory.findById(body.categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify category belongs to this budget
    if (category.budget.toString() !== id) {
      return NextResponse.json(
        { error: 'Category does not belong to this budget' },
        { status: 400 }
      );
    }

    // Update category
    category.name = body.name;
    if (body.description !== undefined) {
      category.description = body.description;
    }
    if (body.type !== undefined) {
      category.type = body.type;
    }

    await category.save();

    // Recalculate budget totals
    await budgetService.calculateBudgetTotals(id);

    // Get updated budget
    const updatedBudget = await Budget.findById(id);

    return NextResponse.json({
      success: true,
      message: 'Category updated successfully',
      category,
      budget: updatedBudget
    });
  } catch (error: unknown) {
    logger.error(`Error updating budget category`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/budget/[id]/categories
 * Delete a category from a budget
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const categoryId = searchParams.get('categoryId');

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    // Find budget
    const budget = await Budget.findById(id);
    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      );
    }

    // Check if budget can be modified
    if (budget.status !== 'draft' && !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ])) {
      return NextResponse.json(
        { error: 'Only draft budgets can be modified by non-admin users' },
        { status: 403 }
      );
    }

    // Find the category
    const category = await BudgetCategory.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify category belongs to this budget
    if (category.budget.toString() !== id) {
      return NextResponse.json(
        { error: 'Category does not belong to this budget' },
        { status: 400 }
      );
    }

    // Delete all subcategories for this category
    await mongoose.models.BudgetSubcategory.deleteMany({
      parentCategory: categoryId
    });

    // Delete all items for this category
    await mongoose.models.BudgetItem.deleteMany({
      parentCategory: categoryId
    });

    // Delete the category
    await BudgetCategory.findByIdAndDelete(categoryId);

    // Recalculate budget totals
    await budgetService.calculateBudgetTotals(id);

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting budget category`, LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}