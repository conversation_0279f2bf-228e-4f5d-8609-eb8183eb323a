// app/api/accounting/budget/[id]/utilization/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { logger } from '@/lib/utils/logger';
// import Income from '@/models/accounting/Income';
// import Expense from '@/models/accounting/Expense';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';
import mongoose from 'mongoose';

// Define interfaces for our data structures
interface CategoryData {
  _id: mongoose.Types.ObjectId;
  name: string;
  type: 'income' | 'expense';
  total: number;
  actualAmount: number;
  budget: mongoose.Types.ObjectId;
  [key: string]: any;
}

interface BudgetData {
  _id: mongoose.Types.ObjectId;
  name: string;
  startDate: Date;
  totalIncome: number;
  totalExpense: number;
  totalActualIncome: number;
  totalActualExpense: number;
  [key: string]: any;
}

interface UtilizationData {
  id: string;
  name: string;
  budgeted: number;
  actual: number;
  variance: number;
  utilizationPercentage: number;
}

export const runtime = 'nodejs';



/**
 * GET /api/accounting/budget/[id]/utilization
 * Fetch budget utilization by category
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Declare budgetId at function scope with default value
  let budgetId: string = 'unknown';

  try {
    // Resolve the params promise
    const { id } = await params;
    budgetId = id;
    // Get session
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Budget ID is already available from params

    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Check if budget exists
    const budget = await Budget.findById(budgetId);
    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get('type') || 'all';

    // Fetch budget categories
    const categoryFilters: Record<string, any> = { budget: budgetId };
    if (type === 'income' || type === 'expense') {
      categoryFilters.type = type;
    }

    const categories = await BudgetCategory.find(categoryFilters)
      .sort({ name: 1 })
      .lean();

    // Process income categories
    const incomeCategories: UtilizationData[] = categories
      .filter(category => category.type === 'income')
      .map(category => {
        const budgeted = category.total || 0;
        const actual = category.actualAmount || 0;
        const variance = actual - budgeted;
        const utilizationPercentage = budgeted > 0 ? (actual / budgeted) * 100 : 0;

        return {
          id: category._id ? category._id.toString() : 'unknown',
          name: category.name || 'Unknown',
          budgeted,
          actual,
          variance,
          utilizationPercentage
        };
      });

    // Process expense categories
    const expenseCategories: UtilizationData[] = categories
      .filter(category => category.type === 'expense')
      .map(category => {
        const budgeted = category.total || 0;
        const actual = category.actualAmount || 0;
        const variance = actual - budgeted;
        const utilizationPercentage = budgeted > 0 ? (actual / budgeted) * 100 : 0;

        return {
          id: category._id ? category._id.toString() : 'unknown',
          name: category.name || 'Unknown',
          budgeted,
          actual,
          variance,
          utilizationPercentage
        };
      });

    // Calculate overall budget utilization
    const budgetedIncome = budget.totalIncome || 0;
    const budgetedExpense = budget.totalExpense || 0;
    const actualIncome = budget.totalActualIncome || 0;
    const actualExpense = budget.totalActualExpense || 0;
    const incomeVariance = actualIncome - budgetedIncome;
    const expenseVariance = actualExpense - budgetedExpense;

    // Return budget utilization data
    return NextResponse.json({
      budgetId: budget._id ? budget._id.toString() : 'unknown',
      name: budget.name || 'Unknown',
      period: { year: budget.startDate ? new Date(budget.startDate).getFullYear() : new Date().getFullYear() },
      budgetedIncome,
      budgetedExpense,
      actualIncome,
      actualExpense,
      incomeVariance,
      expenseVariance,
      incomeCategories,
      expenseCategories
    });
  } catch (error) {
    logger.error('Error fetching budget utilization', error);
    return NextResponse.json(
      { error: 'Failed to fetch budget utilization' },
      { status: 500 }
    );
  }
}
