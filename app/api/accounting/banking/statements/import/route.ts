// app/api/accounting/banking/statements/import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { statementImportService } from '@/lib/services/statement-import-service';
import { StatementFormat } from '@/components/accounting/banking/statement-parser/statement-format-parser';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID
    const userId = user.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 401 }
      );
    }

    // Convert user ID to MongoDB ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // Get form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const bankAccountId = formData.get('bankAccountId') as string;
    const format = formData.get('format') as StatementFormat;
    const dateFormat = formData.get('dateFormat') as string | undefined;
    const columnMapping = formData.get('columnMapping') as string | undefined;

    // Validate required fields
    if (!file || !bankAccountId || !format) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Parse column mapping if provided
    let parsedColumnMapping;
    if (columnMapping) {
      try {
        parsedColumnMapping = JSON.parse(columnMapping);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid column mapping format' },
          { status: 400 }
        );
      }
    }

    // Import statement
    const result = await statementImportService.importStatement(
      buffer,
      {
        bankAccountId,
        dateFormat,
        columnMapping: parsedColumnMapping,
        // Pass format as part of the options
        autoDetectFormat: false,
        format: format as any, // Type assertion to avoid TypeScript error
        createdBy: userObjectId // Add the user who created the import
      }
    );

    return NextResponse.json(result);
  } catch (error: unknown) {
    // Use logger instead of console.error
    logger.error('Error importing bank statement:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
