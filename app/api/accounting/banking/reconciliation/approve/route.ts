// app/api/accounting/banking/reconciliation/approve/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import Reconciliation from '@/models/accounting/Reconciliation';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// Define the request body type
interface ApproveRequestBody {
  id: string;
  action: 'approve' | 'reject';
  notes?: string;
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json() as ApproveRequestBody;
    const { id, action, notes } = body;

    // Ensure user ID is available
    const userId = user.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 401 }
      );
    }

    // Convert user ID to MongoDB ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // Validate required fields
    if (!id || !action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Find reconciliation
    const reconciliation = await Reconciliation.findById(id);
    if (!reconciliation) {
      return NextResponse.json(
        { error: 'Reconciliation not found' },
        { status: 404 }
      );
    }

    // Check if reconciliation is in completed status
    if (reconciliation.status !== 'completed') {
      return NextResponse.json(
        { error: 'Only completed reconciliations can be approved or rejected' },
        { status: 400 }
      );
    }

    // Update reconciliation based on action
    if (action === 'approve') {
      reconciliation.status = 'approved';
      reconciliation.approvedBy = userObjectId;
      reconciliation.approvedAt = new Date();
      if (notes) {
        reconciliation.notes = reconciliation.notes
          ? `${reconciliation.notes}\n\nApproval Notes: ${notes}`
          : `Approval Notes: ${notes}`;
      }
    } else if (action === 'reject') {
      reconciliation.status = 'in_progress';
      reconciliation.updatedBy = userObjectId;
      if (notes) {
        reconciliation.notes = reconciliation.notes
          ? `${reconciliation.notes}\n\nRejection Notes: ${notes}`
          : `Rejection Notes: ${notes}`;
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject"' },
        { status: 400 }
      );
    }

    // Save changes
    await reconciliation.save();

    return NextResponse.json({
      success: true,
      message: action === 'approve' ? 'Reconciliation approved' : 'Reconciliation rejected',
      reconciliation: {
        id: reconciliation._id,
        status: reconciliation.status,
        approvedBy: reconciliation.approvedBy,
        approvedAt: reconciliation.approvedAt,
        updatedBy: reconciliation.updatedBy,
        updatedAt: reconciliation.updatedAt,
      }
    });
  } catch (error: unknown) {
    logger.error('Error approving/rejecting reconciliation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
