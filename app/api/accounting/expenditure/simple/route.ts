// app/api/accounting/expenditure/simple/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expense from '@/models/accounting/Expense';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import budgetIntegrationService from '@/lib/services/accounting/budget-integration-service';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';
import { Budget, BudgetCategory } from '@/models/accounting/Budget';

export const runtime = 'nodejs';

// Enhanced expenditure validation schema with budget integration
const simpleExpenditureSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }),
  category: z.enum(['operational', 'capital', 'personnel', 'administrative', 'travel', 'utilities', 'maintenance', 'supplies', 'professional_services', 'training', 'marketing', 'technology', 'insurance', 'legal', 'other']),
  subcategory: z.string().optional(),
  amount: z.number().positive("Amount must be positive"),
  reference: z.string().min(2, "Reference must be at least 2 characters"),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, "Fiscal year is required"),
  status: z.enum(['draft', 'submitted', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled', 'on_hold']).default('draft'),
  paymentMethod: z.string().optional(),
  vendor: z.string().optional(),
  department: z.string().optional(),
  // Budget integration fields
  budget: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetSubcategory: z.string().optional(),
  appliedToBudget: z.boolean().optional().default(true), // Default to true for automatic integration
  notes: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    console.log('Simple expenditure API called')
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      console.log('Unauthorized access attempt')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('User authenticated:', user.email)

    // Connect to database
    await connectToDatabase();
    console.log('Database connected')

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      console.log('Insufficient permissions for user:', user.email)
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    console.log('Request data received:', data)

    // Validate request body
    const validationResult = simpleExpenditureSchema.safeParse(data);
    if (!validationResult.success) {
      console.log('Validation failed:', validationResult.error.errors)
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    console.log('Data validated successfully')

    // Generate expenditure number
    const expenditureNumber = `EXP-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Prepare expenditure data
    const expenditureData = {
      expenditureNumber,
      title: validationResult.data.description || `${validationResult.data.category} expenditure`,
      description: validationResult.data.description || '',
      category: validationResult.data.category,
      subcategory: validationResult.data.subcategory || 'other',
      amount: validationResult.data.amount,
      currency: 'MWK',
      amountInBaseCurrency: validationResult.data.amount,
      expenditureDate: new Date(validationResult.data.date),
      status: validationResult.data.status,
      priority: 'medium',
      requestedBy: user.id,
      requestedByName: user.name || user.email,
      requestedByEmail: user.email,
      department: validationResult.data.department || 'General',
      vendor: {
        vendorName: validationResult.data.vendor || 'Unknown Vendor',
        isPreferred: false
      },
      budgetAllocations: [],
      approvalWorkflow: [],
      currentApprovalStep: 0,
      receipts: [],
      supportingDocuments: [],
      policyCompliance: {
        isCompliant: true,
        violations: [],
        requiresApproval: false,
        approvalLevel: 0
      },
      taxInfo: {
        taxType: 'none',
        taxRate: 0,
        taxAmount: 0,
        isExempt: true
      },
      tags: [],
      notes: [validationResult.data.notes || ''].filter(Boolean),
      isUrgent: false,
      requiresReceipt: true,
      isCapitalExpenditure: validationResult.data.category === 'capital',
      createdBy: user.id,
      createdAt: new Date(),
      updatedBy: user.id,
      updatedAt: new Date(),
      // Budget fields for integration
      budget: validationResult.data.budget,
      budgetCategory: validationResult.data.budgetCategory,
      budgetSubcategory: validationResult.data.budgetSubcategory,
      appliedToBudget: validationResult.data.appliedToBudget,
    };

    console.log('Creating expenditure with data:', expenditureData)

    // Create expenditure transaction
    const expenditure = await Expense.create(expenditureData);
    console.log('Expenditure created successfully:', expenditure._id)

    // ALWAYS create BudgetExpenditure record for budget planning integration
    // This ensures all expenditures appear in budget stats regardless of approval status
    if (validationResult.data.appliedToBudget && validationResult.data.budget && validationResult.data.budgetCategory) {
      try {
        console.log('Creating BudgetExpenditure record for budget planning...')
        await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expenditure);
        console.log('BudgetExpenditure record created successfully')
      } catch (budgetError) {
        console.warn('BudgetExpenditure creation failed, but expenditure was created:', budgetError)
        // Don't fail the entire operation if budget integration fails
      }
    }

    // Additional budget integration for legacy support
    if (validationResult.data.appliedToBudget) {
      try {
        console.log('Applying expenditure to budget automatically...')
        await budgetIntegrationService.handleNewExpense(expenditure);
        console.log('Expenditure applied to budget successfully')
      } catch (budgetError) {
        console.warn('Budget integration failed, but expenditure was created:', budgetError)
        // Don't fail the entire operation if budget integration fails
      }
    }

    // Populate the created expenditure for response
    const populatedExpenditure = await Expense.findById(expenditure._id)
      .populate('createdBy', 'name email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .lean();

    console.log('Expenditure populated and ready to return')

    return NextResponse.json(
      {
        success: true,
        message: 'Expenditure transaction recorded successfully',
        expenditure: populatedExpenditure
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    console.error('Error in simple expenditure API:', error)
    logger.error('Error recording expenditure transaction (simple)', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET method to fetch expenditures
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Fetch expenditures
    const expenditures = await Expense.find()
      .populate('createdBy', 'email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await Expense.countDocuments();

    return NextResponse.json({
      expenditures,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit)
    });
  } catch (error: unknown) {
    console.error('Error fetching expenditures:', error)
    logger.error('Error fetching expenditures', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
