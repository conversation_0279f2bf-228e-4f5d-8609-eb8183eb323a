// app/api/accounting/expenditure/migrate-to-budget/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { logger } from '@/lib/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Expense from '@/models/accounting/Expense';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';

export const runtime = 'nodejs';

/**
 * POST /api/accounting/expenditure/migrate-to-budget
 * Migrate existing approved expenditures to BudgetExpenditure model
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions - only super admin can run migrations
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Only super admin can run migrations' },
        { status: 403 }
      );
    }

    // Find all approved/paid expenditures that have budget linkage
    const expenditures = await Expense.find({
      status: { $in: ['approved', 'paid'] },
      appliedToBudget: true,
      budget: { $exists: true },
      budgetCategory: { $exists: true }
    }).lean();

    console.log(`Found ${expenditures.length} expenditures to migrate`);

    let migratedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process each expenditure
    for (const expenditure of expenditures) {
      try {
        await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expenditure);
        migratedCount++;
        console.log(`Migrated expenditure ${expenditure._id} to BudgetExpenditure`);
      } catch (error) {
        errorCount++;
        const errorMessage = `Failed to migrate expenditure ${expenditure._id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    // Log the migration results
    logger.info('Expenditure migration completed', {
      totalFound: expenditures.length,
      migratedCount,
      errorCount,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Expenditure migration completed',
      results: {
        totalFound: expenditures.length,
        migratedCount,
        errorCount,
        errors: errors.slice(0, 10) // Limit errors in response
      }
    });

  } catch (error: unknown) {
    console.error('Error during expenditure migration:', error);
    logger.error('Error during expenditure migration', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
