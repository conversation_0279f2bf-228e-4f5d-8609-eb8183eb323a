// app/api/accounting/expenditure/debug/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Expense from '@/models/accounting/Expense';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/expenditure/debug
 * Debug endpoint to check expenditure and budget expenditure data
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get expenditure counts by status
    const expenditureCounts = await Expense.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Get approved expenditures with budget linkage
    const approvedExpenditures = await Expense.find({
      status: { $in: ['approved', 'paid'] },
      appliedToBudget: true,
      budget: { $exists: true },
      budgetCategory: { $exists: true }
    })
    .populate('budget', 'name fiscalYear')
    .populate('budgetCategory', 'name type')
    .limit(10)
    .lean();

    // Get BudgetExpenditure counts
    const budgetExpenditureCounts = await BudgetExpenditure.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Get sample BudgetExpenditure records
    const budgetExpenditures = await BudgetExpenditure.find({})
    .populate('budget', 'name fiscalYear')
    .populate('budgetCategory', 'name type')
    .populate('sourceExpenditure', 'reference description')
    .limit(10)
    .lean();

    return NextResponse.json({
      success: true,
      data: {
        expenditureCounts,
        approvedExpenditures: {
          count: approvedExpenditures.length,
          samples: approvedExpenditures
        },
        budgetExpenditureCounts,
        budgetExpenditures: {
          count: budgetExpenditures.length,
          samples: budgetExpenditures
        }
      }
    });

  } catch (error: unknown) {
    console.error('Error in debug endpoint:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
