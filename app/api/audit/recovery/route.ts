// app/api/audit/recovery/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import DeletedItem from '@/models/audit/DeletedItems';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * POST /api/audit/recovery
 * Recover a deleted item with full audit compliance
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only auditors and super admins can recover items
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to recover audit data' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();
    const { 
      itemId, 
      recoveryReason, 
      recoveryJustification, 
      acknowledgments 
    } = body;

    // Validate required fields
    if (!itemId || !recoveryReason || !recoveryJustification || !acknowledgments) {
      return NextResponse.json(
        { error: 'itemId, recoveryReason, recoveryJustification, and acknowledgments are required' },
        { status: 400 }
      );
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(itemId)) {
      return NextResponse.json(
        { error: 'Invalid item ID format' },
        { status: 400 }
      );
    }

    // Validate recovery reason length (use trimmed value)
    if (!recoveryReason.trim() || recoveryReason.trim().length < 20) {
      return NextResponse.json(
        { error: `Recovery reason must be at least 20 characters long (currently ${recoveryReason.trim().length})` },
        { status: 400 }
      );
    }

    // Validate recovery justification
    if (!recoveryJustification.trim()) {
      return NextResponse.json(
        { error: 'Recovery justification is required' },
        { status: 400 }
      );
    }

    // Validate acknowledgments
    if (!acknowledgments.compliance || !acknowledgments.responsibility || !acknowledgments.auditTrail) {
      return NextResponse.json(
        { error: 'All compliance acknowledgments are required' },
        { status: 400 }
      );
    }

    // Fetch the deleted item
    const deletedItem = await DeletedItem.findById(itemId);
    if (!deletedItem) {
      return NextResponse.json(
        { error: 'Deleted item not found' },
        { status: 404 }
      );
    }

    // Check if item can be recovered
    if (!deletedItem.canBeRecovered) {
      return NextResponse.json(
        { error: 'This item is not eligible for recovery' },
        { status: 400 }
      );
    }

    // Check if recovery deadline has passed
    const now = new Date();
    if (deletedItem.recoveryDeadline && now > deletedItem.recoveryDeadline) {
      return NextResponse.json(
        { error: 'Recovery deadline has passed. This item can no longer be recovered.' },
        { status: 400 }
      );
    }

    // Check if item is flagged
    if (deletedItem.reviewStatus === 'flagged') {
      return NextResponse.json(
        { error: 'Flagged items cannot be recovered. Please contact system administrator.' },
        { status: 400 }
      );
    }

    // Get the original model to determine recovery target
    const originalModel = deletedItem.originalModel;
    const originalData = deletedItem.originalData;

    // Import the appropriate model dynamically
    let TargetModel;
    try {
      switch (originalModel) {
        case 'Income':
          TargetModel = (await import('@/models/Income')).default;
          break;
        case 'Expenditure':
          TargetModel = (await import('@/models/Expenditure')).default;
          break;
        case 'Employee':
          TargetModel = (await import('@/models/Employee')).default;
          break;
        case 'Budget':
          TargetModel = (await import('@/models/Budget')).default;
          break;
        case 'Payroll':
          TargetModel = (await import('@/models/Payroll')).default;
          break;
        default:
          throw new Error(`Unsupported model type: ${originalModel}`);
      }
    } catch (error) {
      console.error('Error importing model:', error);
      return NextResponse.json(
        { error: `Cannot recover ${originalModel} items: Model not available` },
        { status: 500 }
      );
    }

    // Start a transaction for atomic recovery
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Prepare recovery data
      const recoveryData = {
        ...originalData,
        _id: new mongoose.Types.ObjectId(deletedItem.originalId),
        // Add recovery metadata
        recoveredAt: now,
        recoveredBy: user.id,
        recoveryReason,
        recoveryJustification,
        originalDeletionDate: deletedItem.deletedAt,
        auditRecoveryId: deletedItem._id
      };

      // Remove the _id from originalData to avoid conflicts
      delete recoveryData._id;

      // Create new document in original collection
      const recoveredItem = new TargetModel(recoveryData);
      await recoveredItem.save({ session });

      // Update the deleted item to mark as recovered
      await DeletedItem.findByIdAndUpdate(
        itemId,
        {
          $set: {
            canBeRecovered: false,
            reviewStatus: 'approved',
            reviewedBy: user.id,
            reviewedAt: now,
            auditNotes: `Item recovered by ${user.firstName} ${user.lastName}. Reason: ${recoveryReason}`,
            recoveryMetadata: {
              recoveredAt: now,
              recoveredBy: user.id,
              recoveryReason,
              recoveryJustification,
              acknowledgments,
              recoveredItemId: recoveredItem._id,
              recoveryRequestId: new mongoose.Types.ObjectId()
            }
          }
        },
        { session }
      );

      // Commit the transaction
      await session.commitTransaction();

      // Log the recovery action
      console.log(`Item ${itemId} (${originalModel}) recovered by user ${user.id} (${user.email})`);

      return NextResponse.json({
        success: true,
        message: `${originalModel} item successfully recovered`,
        recoveredItem: {
          id: recoveredItem._id.toString(),
          type: originalModel,
          reference: originalData.reference || originalData.name || 'N/A'
        },
        recoveryDetails: {
          recoveredAt: now.toISOString(),
          recoveredBy: {
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            role: user.role
          },
          recoveryReason,
          auditTrailUpdated: true
        }
      });

    } catch (error) {
      // Rollback transaction on error
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }

  } catch (error) {
    console.error('Error during item recovery:', error);
    
    // Handle specific error types
    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json(
        { error: 'Data validation failed during recovery. Original data may be corrupted.' },
        { status: 400 }
      );
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json(
        { error: 'Invalid data format during recovery' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'An error occurred during item recovery' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/audit/recovery
 * Get recovery statistics and recent recovery actions
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get recovery statistics
    const totalRecoverable = await DeletedItem.countDocuments({
      canBeRecovered: true,
      recoveryDeadline: { $gt: new Date() }
    });

    const expiringItems = await DeletedItem.countDocuments({
      canBeRecovered: true,
      recoveryDeadline: { 
        $gt: new Date(),
        $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
      }
    });

    const recoveredToday = await DeletedItem.countDocuments({
      'recoveryMetadata.recoveredAt': {
        $gte: new Date(new Date().setHours(0, 0, 0, 0))
      }
    });

    // Get recent recovery actions
    const recentRecoveries = await DeletedItem.find({
      'recoveryMetadata.recoveredAt': { $exists: true }
    })
    .sort({ 'recoveryMetadata.recoveredAt': -1 })
    .limit(10)
    .select('originalModel originalData recoveryMetadata')
    .lean();

    return NextResponse.json({
      stats: {
        totalRecoverable,
        expiringItems,
        recoveredToday,
        recoveryWindow: 90 // days
      },
      recentRecoveries: recentRecoveries.map(item => ({
        id: item._id,
        type: item.originalModel,
        reference: item.originalData?.reference || item.originalData?.name || 'N/A',
        recoveredAt: item.recoveryMetadata?.recoveredAt,
        recoveredBy: item.recoveryMetadata?.recoveredBy,
        reason: item.recoveryMetadata?.recoveryReason
      }))
    });

  } catch (error) {
    console.error('Error fetching recovery statistics:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching recovery data' },
      { status: 500 }
    );
  }
}
