import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';



// Simple budget category schema for testing
const BudgetCategorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  type: { type: String, enum: ['income', 'expense'], default: 'expense' },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Create model if it doesn't exist
const BudgetCategory = mongoose.models.BudgetCategory || mongoose.model('BudgetCategory', BudgetCategorySchema);

export async function GET(req: NextRequest): Promise<Response> {
  try {
    console.log('Testing budget categories endpoint...');
    
    // Test database connection
    console.log('Step 1: Testing database connection...');
    const connectionStart = Date.now();
    await connectToDatabase();
    const connectionTime = Date.now() - connectionStart;
    console.log(`Database connected in ${connectionTime}ms`);
    
    // Test database ping
    console.log('Step 2: Testing database ping...');
    const pingResult = await mongoose.connection.db.admin().ping();
    console.log('Database ping result:', pingResult);
    
    // Test budget categories query
    console.log('Step 3: Testing budget categories query...');
    const queryStart = Date.now();
    
    try {
      const categories = await BudgetCategory.find({ type: 'expense', isActive: true })
        .select('name description type isActive')
        .limit(10)
        .lean();
      
      const queryTime = Date.now() - queryStart;
      console.log(`Query completed in ${queryTime}ms, found ${categories.length} categories`);
      
      // If no categories found, create some test data
      if (categories.length === 0) {
        console.log('Step 4: No categories found, creating test data...');
        
        const testCategories = [
          { name: 'Professional Services', description: 'Professional and consulting services', type: 'expense' },
          { name: 'Office Supplies', description: 'Office supplies and materials', type: 'expense' },
          { name: 'Equipment & Hardware', description: 'Equipment and hardware purchases', type: 'expense' },
          { name: 'Maintenance & Repairs', description: 'Maintenance and repair services', type: 'expense' },
          { name: 'Utilities & Communications', description: 'Utilities and communication services', type: 'expense' }
        ];
        
        const insertedCategories = await BudgetCategory.insertMany(testCategories);
        console.log(`Created ${insertedCategories.length} test categories`);
        
        return NextResponse.json({
          status: 'success',
          message: 'Budget categories test completed - created test data',
          data: {
            connectionTime: `${connectionTime}ms`,
            queryTime: `${queryTime}ms`,
            categoriesFound: 0,
            categoriesCreated: insertedCategories.length,
            testCategories: insertedCategories
          },
          timestamp: new Date().toISOString()
        });
      }
      
      return NextResponse.json({
        status: 'success',
        message: 'Budget categories test completed successfully',
        data: {
          connectionTime: `${connectionTime}ms`,
          queryTime: `${queryTime}ms`,
          categoriesFound: categories.length,
          categories: categories
        },
        timestamp: new Date().toISOString()
      });
      
    } catch (queryError) {
      console.error('Budget categories query failed:', queryError);
      
      return NextResponse.json({
        status: 'query_failed',
        message: 'Database connected but query failed',
        error: {
          message: queryError instanceof Error ? queryError.message : 'Unknown query error',
          connectionTime: `${connectionTime}ms`
        },
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Budget categories test failed:', error);
    
    const errorInfo = {
      message: error instanceof Error ? error.message : 'Unknown error',
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      database: mongoose.connection.name
    };
    
    logger.error('Budget categories test failed', LogCategory.ACCOUNTING, error);
    
    return NextResponse.json({
      status: 'failed',
      message: 'Budget categories test failed',
      error: errorInfo,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest): Promise<Response> {
  try {
    console.log('Creating test budget categories...');
    
    await connectToDatabase();
    
    const testCategories = [
      { name: 'Professional Services', description: 'Professional and consulting services', type: 'expense' },
      { name: 'Office Supplies', description: 'Office supplies and materials', type: 'expense' },
      { name: 'Equipment & Hardware', description: 'Equipment and hardware purchases', type: 'expense' },
      { name: 'Maintenance & Repairs', description: 'Maintenance and repair services', type: 'expense' },
      { name: 'Utilities & Communications', description: 'Utilities and communication services', type: 'expense' },
      { name: 'Training & Development', description: 'Training and development programs', type: 'expense' },
      { name: 'Other Expenses', description: 'Other miscellaneous expenses', type: 'expense' }
    ];
    
    // Clear existing test categories
    await BudgetCategory.deleteMany({ name: { $in: testCategories.map(c => c.name) } });
    
    // Insert new test categories
    const insertedCategories = await BudgetCategory.insertMany(testCategories);
    
    return NextResponse.json({
      status: 'success',
      message: 'Test budget categories created successfully',
      data: {
        categoriesCreated: insertedCategories.length,
        categories: insertedCategories
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Failed to create test budget categories:', error);
    
    return NextResponse.json({
      status: 'failed',
      message: 'Failed to create test budget categories',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
